version: 4.21.1
installed_at: '2025-07-02T08:23:58.859Z'
install_type: full
agent: null
ides_setup: []
expansion_packs:
  - bmad-2d-phaser-game-dev
  - bmad-creator-tools
  - bmad-infrastructure-devops
files:
  - path: .bmad-core/core-config.yml
    hash: aa4085ba2d6e5466
    modified: false
  - path: .bmad-core/workflows/greenfield-ui.yml
    hash: bfbf56a0d93a5408
    modified: false
  - path: .bmad-core/workflows/greenfield-service.yml
    hash: a48c3f9e2c8d4453
    modified: false
  - path: .bmad-core/workflows/greenfield-fullstack.yml
    hash: 0cdf886130cc6abb
    modified: false
  - path: .bmad-core/workflows/brownfield-ui.yml
    hash: c574ad2dceca79e8
    modified: false
  - path: .bmad-core/workflows/brownfield-service.yml
    hash: 289c80b8b51ec26b
    modified: false
  - path: .bmad-core/workflows/brownfield-fullstack.yml
    hash: 509c6f109b01e2b7
    modified: false
  - path: .bmad-core/utils/workflow-management.md
    hash: 11fa4807701a4466
    modified: false
  - path: .bmad-core/utils/template-format.md
    hash: c91208908af1dc78
    modified: false
  - path: .bmad-core/utils/plan-management.md
    hash: 32593c033c2dc6f6
    modified: false
  - path: .bmad-core/templates/story-tmpl.md
    hash: 3d8d98f09c3bcd3a
    modified: false
  - path: .bmad-core/templates/project-brief-tmpl.md
    hash: 4574687e30632eed
    modified: false
  - path: .bmad-core/templates/prd-tmpl.md
    hash: b5d75306c428615b
    modified: false
  - path: .bmad-core/templates/market-research-tmpl.md
    hash: 3e0481172cc9f056
    modified: false
  - path: .bmad-core/templates/fullstack-architecture-tmpl.md
    hash: 969ca155b5832956
    modified: false
  - path: .bmad-core/templates/front-end-spec-tmpl.md
    hash: fb4866eaeb8722fb
    modified: false
  - path: .bmad-core/templates/front-end-architecture-tmpl.md
    hash: 84025d1fc469d2c5
    modified: false
  - path: .bmad-core/templates/competitor-analysis-tmpl.md
    hash: d4e25586a643f8ca
    modified: false
  - path: .bmad-core/templates/brownfield-prd-tmpl.md
    hash: 5046356b009971fc
    modified: false
  - path: .bmad-core/templates/brownfield-architecture-tmpl.md
    hash: c7b0962d7e856aae
    modified: false
  - path: .bmad-core/templates/architecture-tmpl.md
    hash: b8daeb43d0196ee1
    modified: false
  - path: .bmad-core/tasks/update-workflow-plan.md
    hash: 3065a46b3f648637
    modified: false
  - path: .bmad-core/tasks/shard-doc.md
    hash: 0a04158f420a09df
    modified: false
  - path: .bmad-core/tasks/review-story.md
    hash: cb88d2aed30afe7d
    modified: false
  - path: .bmad-core/tasks/kb-mode-interaction.md
    hash: 97623a1fbcb686e4
    modified: false
  - path: .bmad-core/tasks/index-docs.md
    hash: 349f0ddf65dd71fe
    modified: false
  - path: .bmad-core/tasks/generate-ai-frontend-prompt.md
    hash: 1298309d4e874b0b
    modified: false
  - path: .bmad-core/tasks/execute-checklist.md
    hash: e0467201115d500f
    modified: false
  - path: .bmad-core/tasks/document-project.md
    hash: 6daded58e413997d
    modified: false
  - path: .bmad-core/tasks/doc-migration-task.md
    hash: 6a86e5d9cd386452
    modified: false
  - path: .bmad-core/tasks/create-workflow-plan.md
    hash: 46071b141d5e3b71
    modified: false
  - path: .bmad-core/tasks/create-next-story.md
    hash: 0d40c2b775fa3cb4
    modified: false
  - path: .bmad-core/tasks/create-doc.md
    hash: c484e12a587cab63
    modified: false
  - path: .bmad-core/tasks/create-deep-research-prompt.md
    hash: a104cb9b17cde3b7
    modified: false
  - path: .bmad-core/tasks/create-brownfield-story.md
    hash: 8a92a1721d165d88
    modified: false
  - path: .bmad-core/tasks/correct-course.md
    hash: 4da2f889fbc8b457
    modified: false
  - path: .bmad-core/tasks/brownfield-create-story.md
    hash: 6e5cd0247836c4de
    modified: false
  - path: .bmad-core/tasks/brownfield-create-epic.md
    hash: 1b2b6c8b67a176ee
    modified: false
  - path: .bmad-core/tasks/brainstorming-techniques.md
    hash: 075818b207444cb2
    modified: false
  - path: .bmad-core/tasks/advanced-elicitation.md
    hash: 0950e5318989db16
    modified: false
  - path: .bmad-core/data/technical-preferences.md
    hash: 6530bed845540b0d
    modified: false
  - path: .bmad-core/data/bmad-kb.md
    hash: 7795e838ee83dd16
    modified: false
  - path: .bmad-core/checklists/story-draft-checklist.md
    hash: 28d5c2d3e9088320
    modified: false
  - path: .bmad-core/checklists/story-dod-checklist.md
    hash: 06ab7e73a69f930a
    modified: false
  - path: .bmad-core/checklists/po-master-checklist.md
    hash: 89d2dc785aa0e8a7
    modified: false
  - path: .bmad-core/checklists/pm-checklist.md
    hash: 139209e205a92628
    modified: false
  - path: .bmad-core/checklists/change-checklist.md
    hash: 5f2e21564d452d35
    modified: false
  - path: .bmad-core/checklists/architect-checklist.md
    hash: 99f4655b9ff99dd1
    modified: false
  - path: .bmad-core/agents/ux-expert.md
    hash: db26962b08a17c7b
    modified: false
  - path: .bmad-core/agents/sm.md
    hash: 92ff5d6215ca2dcb
    modified: false
  - path: .bmad-core/agents/qa.md
    hash: be99105e59b52422
    modified: false
  - path: .bmad-core/agents/po.md
    hash: 76d33c1721142f31
    modified: false
  - path: .bmad-core/agents/pm.md
    hash: 40f796bdefcd14c6
    modified: false
  - path: .bmad-core/agents/dev.md
    hash: d88c2dc2669a9e90
    modified: false
  - path: .bmad-core/agents/bmad-orchestrator.md
    hash: 53c2123b5263059d
    modified: false
  - path: .bmad-core/agents/bmad-master.md
    hash: 49573fe6b6a4936d
    modified: false
  - path: .bmad-core/agents/architect.md
    hash: 8c7f7244f7394781
    modified: false
  - path: .bmad-core/agents/analyst.md
    hash: f04c2ac401442edc
    modified: false
  - path: .bmad-core/agent-teams/team-no-ui.yml
    hash: db791eaa9b060ad3
    modified: false
  - path: .bmad-core/agent-teams/team-ide-minimal.yml
    hash: 600b6795116fd74e
    modified: false
  - path: .bmad-core/agent-teams/team-fullstack.yml
    hash: 8adda579fff343cd
    modified: false
  - path: .bmad-core/agent-teams/team-all.yml
    hash: 788067c0fd19c76d
    modified: false
  - path: .bmad-2d-phaser-game-dev/agents/game-sm.md
    hash: f243ad5133343fdf
    modified: false
  - path: .bmad-2d-phaser-game-dev/agents/game-developer.md
    hash: f3ef145e8d940deb
    modified: false
  - path: .bmad-2d-phaser-game-dev/agents/game-designer.md
    hash: ceda694aa5939de5
    modified: false
  - path: .bmad-2d-phaser-game-dev/agent-teams/phaser-2d-nodejs-game-team.yml
    hash: 582f1dd4225fe54a
    modified: false
  - path: .bmad-2d-phaser-game-dev/templates/level-design-doc-tmpl.md
    hash: 175ae1869c39a754
    modified: false
  - path: .bmad-2d-phaser-game-dev/templates/game-story-tmpl.md
    hash: 1e6c8afd85acaddb
    modified: false
  - path: .bmad-2d-phaser-game-dev/templates/game-design-doc-tmpl.md
    hash: 0a14814c66e51279
    modified: false
  - path: .bmad-2d-phaser-game-dev/templates/game-brief-tmpl.md
    hash: 261b7a89249347e5
    modified: false
  - path: .bmad-2d-phaser-game-dev/templates/game-architecture-tmpl.md
    hash: f7a9ba115dfaa3d4
    modified: false
  - path: .bmad-2d-phaser-game-dev/tasks/game-design-brainstorming.md
    hash: fe608dd7b1cbfe82
    modified: false
  - path: .bmad-2d-phaser-game-dev/tasks/create-game-story.md
    hash: 79548d0d9e0a41dc
    modified: false
  - path: .bmad-2d-phaser-game-dev/tasks/advanced-elicitation.md
    hash: c7bee8d283900b1c
    modified: false
  - path: .bmad-2d-phaser-game-dev/checklists/game-story-dod-checklist.md
    hash: 1c04d3dac10a357a
    modified: false
  - path: .bmad-2d-phaser-game-dev/checklists/game-design-checklist.md
    hash: 0f802420a3e7f7ed
    modified: false
  - path: .bmad-2d-phaser-game-dev/workflows/game-prototype.yml
    hash: ddb28541f1b4aad4
    modified: false
  - path: .bmad-2d-phaser-game-dev/workflows/game-dev-greenfield.yml
    hash: 12cfcbc74c9a2416
    modified: false
  - path: .bmad-2d-phaser-game-dev/data/development-guidelines.md
    hash: a4ca049daf82a096
    modified: false
  - path: .bmad-2d-phaser-game-dev/data/bmad-kb.md
    hash: 52df2eb0e8322aaa
    modified: false
  - path: .bmad-2d-phaser-game-dev/config.yml
    hash: 956cafe3e14031bf
    modified: false
  - path: .bmad-creator-tools/agents/bmad-the-creator.md
    hash: 3b72c28907a0be15
    modified: false
  - path: .bmad-creator-tools/templates/expansion-pack-plan-tmpl.md
    hash: 64e45fc281a96560
    modified: false
  - path: .bmad-creator-tools/templates/agent-tmpl.md
    hash: 8ce01ebd5480ffde
    modified: false
  - path: .bmad-creator-tools/templates/agent-teams-tmpl.md
    hash: ad2f04b0b6e21633
    modified: false
  - path: .bmad-creator-tools/tasks/generate-expansion-pack.md
    hash: e60073a4b526c5a2
    modified: false
  - path: .bmad-creator-tools/tasks/create-agent.md
    hash: 8abb08933ea3cfb6
    modified: false
  - path: .bmad-creator-tools/config.yml
    hash: 2fe64d02565615fc
    modified: false
  - path: .bmad-creator-tools/README.md
    hash: fddcdcc4375d007f
    modified: false
  - path: .bmad-infrastructure-devops/agents/infra-devops-platform.md
    hash: 063294311d03ebdf
    modified: false
  - path: >-
      .bmad-infrastructure-devops/templates/infrastructure-platform-from-arch-tmpl.md
    hash: b0a0f160b989cd68
    modified: false
  - path: .bmad-infrastructure-devops/templates/infrastructure-architecture-tmpl.md
    hash: 1465d14985aca5cf
    modified: false
  - path: .bmad-infrastructure-devops/tasks/validate-infrastructure.md
    hash: 204348a3617ee91b
    modified: false
  - path: .bmad-infrastructure-devops/tasks/review-infrastructure.md
    hash: 16f31fb41114cbb2
    modified: false
  - path: .bmad-infrastructure-devops/checklists/infrastructure-checklist.md
    hash: b3dc0a7ec1696c6c
    modified: false
  - path: .bmad-infrastructure-devops/data/bmad-kb.md
    hash: ab414992de0d141a
    modified: false
  - path: .bmad-infrastructure-devops/config.yml
    hash: d2f860951a2bd8ad
    modified: false
  - path: .bmad-infrastructure-devops/README.md
    hash: 0286b59e5dce549e
    modified: false
