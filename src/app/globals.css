@import "tailwindcss";

:root {
  /* Light theme colors */
  --background: #ffffff;
  --foreground: #171717;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --border: #e5e5e5;
  --input: #ffffff;
  --primary: #171717;
  --primary-foreground: #ffffff;
  --secondary: #f5f5f5;
  --secondary-foreground: #171717;
  --accent: #f5f5f5;
  --accent-foreground: #171717;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --ring: #171717;
  --radius: 0.5rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-ring: var(--ring);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --radius: var(--radius);
}

/* Dark theme - both system preference and manual toggle */
@media (prefers-color-scheme: dark) {
  :root:not(.light) {
    /* Dark theme colors */
    --background: #0a0a0a;
    --foreground: #ededed;
    --muted: #262626;
    --muted-foreground: #a3a3a3;
    --border: #262626;
    --input: #262626;
    --primary: #ededed;
    --primary-foreground: #171717;
    --secondary: #262626;
    --secondary-foreground: #ededed;
    --accent: #262626;
    --accent-foreground: #ededed;
    --destructive: #dc2626;
    --destructive-foreground: #ededed;
    --ring: #525252;
  }
}

/* Manual dark mode toggle */
.dark {
  /* Dark theme colors */
  --background: #0a0a0a;
  --foreground: #ededed;
  --muted: #262626;
  --muted-foreground: #a3a3a3;
  --border: #262626;
  --input: #262626;
  --primary: #ededed;
  --primary-foreground: #171717;
  --secondary: #262626;
  --secondary-foreground: #ededed;
  --accent: #262626;
  --accent-foreground: #ededed;
  --destructive: #dc2626;
  --destructive-foreground: #ededed;
  --ring: #525252;
}

/* Force light mode */
.light {
  /* Light theme colors */
  --background: #ffffff;
  --foreground: #171717;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --border: #e5e5e5;
  --input: #ffffff;
  --primary: #171717;
  --primary-foreground: #ffffff;
  --secondary: #f5f5f5;
  --secondary-foreground: #171717;
  --accent: #f5f5f5;
  --accent-foreground: #171717;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --ring: #171717;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

/* Selection styles */
::selection {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

/* Grid pattern background */
.bg-grid-pattern {
  background-image:
    linear-gradient(to right, var(--muted-foreground) 1px, transparent 1px),
    linear-gradient(to bottom, var(--muted-foreground) 1px, transparent 1px);
  background-size: 40px 40px;
  opacity: 0.1;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, var(--primary), var(--primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Blog content styles */
.blog-content {
  line-height: 1.8;
}

.blog-content h1,
.blog-content h2,
.blog-content h3,
.blog-content h4,
.blog-content h5,
.blog-content h6 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.3;
}

.blog-content h1 {
  font-size: 2.25rem;
  border-bottom: 2px solid var(--border);
  padding-bottom: 0.5rem;
}

.blog-content h2 {
  font-size: 1.875rem;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.25rem;
}

.blog-content h3 {
  font-size: 1.5rem;
}

.blog-content h4 {
  font-size: 1.25rem;
}

.blog-content p {
  margin-bottom: 1.5rem;
}

.blog-content ul,
.blog-content ol {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.blog-content li {
  margin-bottom: 0.5rem;
}

.blog-content blockquote {
  border-left: 4px solid var(--primary);
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  background: var(--muted);
  padding: 1rem;
  border-radius: 0.5rem;
}

.blog-content code {
  background: var(--muted);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-family: var(--font-mono), monospace;
}

.blog-content pre {
  background: var(--muted);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.blog-content pre code {
  background: none;
  padding: 0;
}

.blog-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
}

.blog-content th,
.blog-content td {
  border: 1px solid var(--border);
  padding: 0.75rem;
  text-align: left;
}

.blog-content th {
  background: var(--muted);
  font-weight: 600;
}

.blog-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1.5rem 0;
}

.blog-content a {
  color: var(--primary);
  text-decoration: underline;
  text-decoration-color: var(--primary);
  text-underline-offset: 2px;
}

.blog-content a:hover {
  text-decoration-thickness: 2px;
}
