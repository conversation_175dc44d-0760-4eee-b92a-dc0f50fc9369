import { NextResponse } from 'next/server';
import { generateBlogRSS } from '@/lib/rss';

export async function GET() {
  try {
    const rssXML = await generateBlogRSS();
    
    return new NextResponse(rssXML, {
      status: 200,
      headers: {
        'Content-Type': 'application/rss+xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // 缓存1小时
      },
    });
  } catch (error) {
    console.error('Error generating RSS feed:', error);
    return new NextResponse('Error generating RSS feed', { status: 500 });
  }
}
