import { Metadata } from "next";
import { notFound } from "next/navigation";
import Link from "next/link";
import { getPhotoById, getRelatedPhotos, formatPhotoDate } from "@/lib/photography";
import { Image } from "@/components/ui/Image";
import { Button } from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { PHOTOGRAPHY_CONFIG } from "@/lib/constants";

interface PhotoPageProps {
  params: Promise<{ id: string }>;
}

export async function generateMetadata({ params }: PhotoPageProps): Promise<Metadata> {
  const { id } = await params;
  const photo = await getPhotoById(id);

  if (!photo) {
    return {
      title: "照片未找到",
      description: "请求的照片不存在",
    };
  }

  return {
    title: `${photo.title} - 摄影作品`,
    description: photo.description || `${photo.title} - 摄影作品详情`,
    openGraph: {
      title: photo.title,
      description: photo.description || `${photo.title} - 摄影作品详情`,
      images: [
        {
          url: photo.src,
          width: 1200,
          height: 800,
          alt: photo.alt,
        },
      ],
    },
  };
}

export default async function PhotoPage({ params }: PhotoPageProps) {
  const { id } = await params;
  const photo = await getPhotoById(id);

  if (!photo) {
    notFound();
  }

  const relatedPhotos = await getRelatedPhotos(id, 4);
  const category = PHOTOGRAPHY_CONFIG.categories.find(cat => cat.id === photo.category);

  return (
    <div className="min-h-screen bg-background">
      {/* 返回按钮 */}
      <div className="container mx-auto px-4 py-6">
        <Link href="/photography">
          <Button variant="outline" size="sm">
            ← 返回作品集
          </Button>
        </Link>
      </div>

      {/* 主要内容区域 */}
      <div className="container mx-auto px-4 pb-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧：照片展示 */}
          <div className="lg:col-span-2">
            <div className="sticky top-6">
              <Card className="overflow-hidden">
                <div className="aspect-[4/3] relative bg-muted">
                  <Image
                    src={photo.src}
                    alt={photo.alt}
                    fill
                    className="object-cover"
                    priority
                    sizes="(max-width: 1024px) 100vw, 66vw"
                  />
                </div>
              </Card>
            </div>
          </div>

          {/* 右侧：照片信息 */}
          <div className="space-y-6">
            {/* 基本信息 */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                {photo.featured && (
                  <span className="px-2 py-1 bg-yellow-500 text-white text-xs rounded-full">
                    ⭐ 精选
                  </span>
                )}
                <span className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md">
                  {category?.name || photo.category}
                </span>
              </div>
              
              <h1 className="text-3xl font-bold text-foreground mb-3">
                {photo.title}
              </h1>
              
              {photo.description && (
                <p className="text-muted-foreground text-lg leading-relaxed">
                  {photo.description}
                </p>
              )}
            </div>

            {/* 拍摄信息 */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">📷 拍摄信息</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">拍摄日期</span>
                  <span className="text-foreground">{formatPhotoDate(photo.date)}</span>
                </div>
                
                {photo.location && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">拍摄地点</span>
                    <span className="text-foreground">{photo.location}</span>
                  </div>
                )}
                
                {photo.camera && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">相机</span>
                    <span className="text-foreground">{photo.camera}</span>
                  </div>
                )}
                
                {photo.lens && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">镜头</span>
                    <span className="text-foreground">{photo.lens}</span>
                  </div>
                )}
              </div>
            </Card>

            {/* 拍摄参数 */}
            {photo.settings && (
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">⚙️ 拍摄参数</h3>
                <div className="grid grid-cols-2 gap-3">
                  {photo.settings.aperture && (
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <div className="text-sm text-muted-foreground">光圈</div>
                      <div className="font-semibold text-foreground">{photo.settings.aperture}</div>
                    </div>
                  )}
                  
                  {photo.settings.shutter && (
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <div className="text-sm text-muted-foreground">快门</div>
                      <div className="font-semibold text-foreground">{photo.settings.shutter}</div>
                    </div>
                  )}
                  
                  {photo.settings.iso && (
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <div className="text-sm text-muted-foreground">ISO</div>
                      <div className="font-semibold text-foreground">{photo.settings.iso}</div>
                    </div>
                  )}
                  
                  {photo.settings.focalLength && (
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <div className="text-sm text-muted-foreground">焦距</div>
                      <div className="font-semibold text-foreground">{photo.settings.focalLength}</div>
                    </div>
                  )}
                </div>
              </Card>
            )}

            {/* 标签 */}
            {photo.tags.length > 0 && (
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">🏷️ 标签</h3>
                <div className="flex flex-wrap gap-2">
                  {photo.tags.map((tag) => (
                    <span
                      key={tag}
                      className="px-3 py-1 bg-muted text-muted-foreground text-sm rounded-md hover:bg-muted/80 transition-colors"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </Card>
            )}
          </div>
        </div>

        {/* 相关作品 */}
        {relatedPhotos.length > 0 && (
          <section className="mt-16">
            <h2 className="text-2xl font-bold text-foreground mb-8 text-center">
              🔗 相关作品
            </h2>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedPhotos.map((relatedPhoto) => (
                <Link key={relatedPhoto.id} href={`/photography/${relatedPhoto.id}`}>
                  <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer group">
                    <div className="aspect-square relative bg-muted">
                      <Image
                        src={relatedPhoto.src}
                        alt={relatedPhoto.alt}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                        sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
                      />
                      
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
                      
                      <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <h3 className="text-white font-semibold text-sm">
                          {relatedPhoto.title}
                        </h3>
                        <p className="text-white/80 text-xs">
                          {category?.name || relatedPhoto.category}
                        </p>
                      </div>
                    </div>
                  </Card>
                </Link>
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
}
