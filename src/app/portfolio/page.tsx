import { Metadata } from "next";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { SITE_CONFIG } from "@/lib/constants";

export const metadata: Metadata = {
  title: "作品集",
  description: "查看我的项目作品和开发案例",
};

// 模拟项目数据
const mockProjects = [
  {
    id: "personal-blog",
    title: "个人博客网站",
    description: "基于 Next.js 15 和 TailwindCSS 4 构建的现代化个人博客",
    longDescription: "这是一个功能完整的个人博客网站，采用最新的 Next.js 15 App Router 架构，结合 TailwindCSS 4 实现响应式设计。支持 Markdown 文章渲染、暗黑模式切换、图片优化等功能。",
    image: "/images/projects/blog-preview.jpg",
    tags: ["个人项目", "博客"],
    technologies: ["Next.js", "React", "TailwindCSS", "TypeScript", "Markdown"],
    links: {
      demo: "https://zer-blog.vercel.app",
      github: "https://github.com/zer/blog",
    },
    featured: true,
    status: "completed" as const,
    date: "2024-12-28",
  },
  {
    id: "ecommerce-platform",
    title: "电商管理平台",
    description: "全栈电商管理系统，支持商品管理、订单处理、用户管理等功能",
    longDescription: "一个完整的电商管理平台，前端使用 React + TypeScript，后端采用 Node.js + Express，数据库使用 PostgreSQL。实现了商品管理、订单处理、用户权限管理、数据统计等核心功能。",
    image: "/images/projects/ecommerce-preview.jpg",
    tags: ["商业项目", "全栈开发"],
    technologies: ["React", "Node.js", "PostgreSQL", "Express", "Redux"],
    links: {
      demo: "https://ecommerce-demo.vercel.app",
      github: "https://github.com/zer/ecommerce-platform",
    },
    featured: true,
    status: "completed" as const,
    date: "2024-11-15",
  },
  {
    id: "task-management",
    title: "任务管理工具",
    description: "团队协作任务管理应用，支持项目管理、任务分配、进度跟踪",
    longDescription: "为团队协作设计的任务管理工具，支持项目创建、任务分配、进度跟踪、团队协作等功能。采用现代化的 UI 设计，提供优秀的用户体验。",
    image: "/images/projects/task-management-preview.jpg",
    tags: ["工具应用", "团队协作"],
    technologies: ["Vue.js", "Python", "Django", "PostgreSQL", "WebSocket"],
    links: {
      demo: "https://task-manager-demo.vercel.app",
      github: "https://github.com/zer/task-management",
    },
    featured: false,
    status: "completed" as const,
    date: "2024-10-20",
  },
  {
    id: "weather-app",
    title: "天气预报应用",
    description: "基于地理位置的天气预报应用，支持多城市、天气预警等功能",
    longDescription: "一个功能丰富的天气预报应用，支持基于地理位置的天气查询、多城市管理、天气预警、历史天气数据查看等功能。界面简洁美观，数据准确及时。",
    image: "/images/projects/weather-app-preview.jpg",
    tags: ["移动应用", "API集成"],
    technologies: ["React Native", "TypeScript", "Weather API", "AsyncStorage"],
    links: {
      demo: "https://weather-app-demo.vercel.app",
      github: "https://github.com/zer/weather-app",
    },
    featured: false,
    status: "completed" as const,
    date: "2024-09-10",
  },
  {
    id: "component-library",
    title: "React 组件库",
    description: "可复用的 React 组件库，包含常用 UI 组件和工具函数",
    longDescription: "为提高开发效率而创建的 React 组件库，包含按钮、表单、模态框、表格等常用 UI 组件，以及一系列实用的工具函数。支持 TypeScript，提供完整的类型定义。",
    image: "/images/projects/component-library-preview.jpg",
    tags: ["开源项目", "组件库"],
    technologies: ["React", "TypeScript", "Storybook", "Rollup", "Jest"],
    links: {
      demo: "https://ui-components-demo.vercel.app",
      github: "https://github.com/zer/react-components",
    },
    featured: false,
    status: "in-progress" as const,
    date: "2024-08-15",
  },
];

export default function PortfolioPage() {
  const featuredProjects = mockProjects.filter(project => project.featured);
  const otherProjects = mockProjects.filter(project => !project.featured);

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "已完成";
      case "in-progress":
        return "进行中";
      case "planned":
        return "计划中";
      default:
        return "未知";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "in-progress":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "planned":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Header */}
      <section className="text-center py-12">
        <h1 className="text-4xl font-bold text-foreground mb-4">项目作品</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          展示我的开发项目和技术实践
        </p>
      </section>

      {/* Featured Projects */}
      {featuredProjects.length > 0 && (
        <section className="py-8">
          <h2 className="text-2xl font-bold text-foreground mb-6">精选项目</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {featuredProjects.map((project) => (
              <Card key={project.id} className="hover:shadow-lg transition-shadow">
                <div className="aspect-video bg-muted rounded-t-lg flex items-center justify-center">
                  <span className="text-4xl">🚀</span>
                </div>
                <CardHeader>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">
                      {new Date(project.date).toLocaleDateString('zh-CN')}
                    </span>
                    <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(project.status)}`}>
                      {getStatusText(project.status)}
                    </span>
                  </div>
                  <CardTitle className="text-xl">{project.title}</CardTitle>
                  <CardDescription>{project.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.technologies.map((tech) => (
                      <span
                        key={tech}
                        className="px-2 py-1 bg-secondary text-secondary-foreground text-xs rounded-md"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    {project.links.demo && (
                      <Button asChild size="sm">
                        <a href={project.links.demo} target="_blank" rel="noopener noreferrer">
                          查看演示
                        </a>
                      </Button>
                    )}
                    {project.links.github && (
                      <Button asChild variant="outline" size="sm">
                        <a href={project.links.github} target="_blank" rel="noopener noreferrer">
                          源码
                        </a>
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      )}

      {/* Other Projects */}
      <section className="py-8">
        <h2 className="text-2xl font-bold text-foreground mb-6">其他项目</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {otherProjects.map((project) => (
            <Card key={project.id} className="hover:shadow-md transition-shadow">
              <div className="aspect-video bg-muted rounded-t-lg flex items-center justify-center">
                <span className="text-3xl">💻</span>
              </div>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-muted-foreground">
                    {new Date(project.date).toLocaleDateString('zh-CN')}
                  </span>
                  <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(project.status)}`}>
                    {getStatusText(project.status)}
                  </span>
                </div>
                <CardTitle className="text-lg">{project.title}</CardTitle>
                <CardDescription className="text-sm line-clamp-2">
                  {project.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex flex-wrap gap-1 mb-3">
                  {project.technologies.slice(0, 3).map((tech) => (
                    <span
                      key={tech}
                      className="px-2 py-1 bg-secondary text-secondary-foreground text-xs rounded-md"
                    >
                      {tech}
                    </span>
                  ))}
                  {project.technologies.length > 3 && (
                    <span className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md">
                      +{project.technologies.length - 3}
                    </span>
                  )}
                </div>
                <div className="flex gap-2">
                  {project.links.demo && (
                    <Button asChild size="sm" className="flex-1">
                      <a href={project.links.demo} target="_blank" rel="noopener noreferrer">
                        演示
                      </a>
                    </Button>
                  )}
                  {project.links.github && (
                    <Button asChild variant="outline" size="sm" className="flex-1">
                      <a href={project.links.github} target="_blank" rel="noopener noreferrer">
                        源码
                      </a>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-12 bg-muted/30 rounded-2xl mt-12">
        <div className="max-w-2xl mx-auto text-center px-8">
          <h2 className="text-2xl font-bold text-foreground mb-4">项目合作</h2>
          <p className="text-muted-foreground mb-6">
            如果你有有趣的项目想要合作，或者需要技术咨询，欢迎联系我
          </p>
          <Button asChild size="lg">
            <Link href="/about">联系我</Link>
          </Button>
        </div>
      </section>
    </div>
  );
}
