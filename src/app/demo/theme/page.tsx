"use client";

import * as React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { ThemeSelector, ThemeToggle } from "@/components/ui/ThemeToggle";
import { useTheme } from "@/components/providers/ThemeProvider";

export default function ThemeDemoPage() {
  const { theme, resolvedTheme, systemTheme } = useTheme();

  return (
    <div className="container mx-auto px-4 py-12 max-w-6xl">
      {/* 页面标题 */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-foreground mb-4">🎨 主题系统演示</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          体验我们的主题系统，支持浅色、深色和跟随系统三种模式
        </p>
      </div>

      {/* 主题控制区域 */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="text-center">主题控制</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center space-y-6">
            <ThemeSelector className="w-full max-w-md" />
            
            <div className="flex items-center gap-4">
              <span className="text-sm text-muted-foreground">快速切换:</span>
              <ThemeToggle />
              <ThemeToggle showLabel variant="button" />
            </div>

            <div className="bg-muted/50 rounded-lg p-4 w-full max-w-md">
              <div className="text-center space-y-2 text-sm">
                <div>
                  <span className="text-muted-foreground">当前选择:</span>
                  <span className="ml-2 font-medium text-foreground">
                    {theme === "light" && "☀️ 浅色模式"}
                    {theme === "dark" && "🌙 深色模式"}
                    {theme === "system" && "💻 跟随系统"}
                  </span>
                </div>
                <div>
                  <span className="text-muted-foreground">实际显示:</span>
                  <span className="ml-2 font-medium text-foreground">
                    {resolvedTheme === "light" ? "☀️ 浅色" : "🌙 深色"}
                  </span>
                </div>
                <div>
                  <span className="text-muted-foreground">系统偏好:</span>
                  <span className="ml-2 font-medium text-foreground">
                    {systemTheme === "light" ? "☀️ 浅色" : "🌙 深色"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 组件展示区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* 按钮组件 */}
        <Card>
          <CardHeader>
            <CardTitle>按钮组件</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Button>默认按钮</Button>
              <Button variant="secondary">次要按钮</Button>
              <Button variant="outline">轮廓按钮</Button>
              <Button variant="ghost">幽灵按钮</Button>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button size="sm">小按钮</Button>
              <Button size="lg">大按钮</Button>
              <Button disabled>禁用按钮</Button>
            </div>
          </CardContent>
        </Card>

        {/* 卡片组件 */}
        <Card>
          <CardHeader>
            <CardTitle>卡片组件</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Card className="p-4">
                <h4 className="font-semibold text-foreground mb-2">嵌套卡片</h4>
                <p className="text-muted-foreground text-sm">
                  这是一个嵌套在另一个卡片中的卡片，展示了主题系统的层次效果。
                </p>
              </Card>
              
              <div className="bg-muted rounded-lg p-4">
                <h4 className="font-semibold text-foreground mb-2">背景区域</h4>
                <p className="text-muted-foreground text-sm">
                  使用 muted 背景色的区域，提供了良好的视觉层次。
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 颜色展示 */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>颜色系统</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <div className="h-16 bg-background border rounded-lg flex items-center justify-center">
                <span className="text-foreground text-sm font-medium">Background</span>
              </div>
              <p className="text-xs text-center text-muted-foreground">背景色</p>
            </div>
            
            <div className="space-y-2">
              <div className="h-16 bg-muted rounded-lg flex items-center justify-center">
                <span className="text-muted-foreground text-sm font-medium">Muted</span>
              </div>
              <p className="text-xs text-center text-muted-foreground">柔和色</p>
            </div>
            
            <div className="space-y-2">
              <div className="h-16 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-primary-foreground text-sm font-medium">Primary</span>
              </div>
              <p className="text-xs text-center text-muted-foreground">主色调</p>
            </div>
            
            <div className="space-y-2">
              <div className="h-16 bg-secondary rounded-lg flex items-center justify-center">
                <span className="text-secondary-foreground text-sm font-medium">Secondary</span>
              </div>
              <p className="text-xs text-center text-muted-foreground">次色调</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 文本展示 */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>文本样式</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h1 className="text-4xl font-bold text-foreground mb-2">标题 1</h1>
            <h2 className="text-3xl font-semibold text-foreground mb-2">标题 2</h2>
            <h3 className="text-2xl font-medium text-foreground mb-2">标题 3</h3>
            <h4 className="text-xl font-medium text-foreground mb-2">标题 4</h4>
          </div>
          
          <div className="space-y-2">
            <p className="text-foreground">
              这是正常的文本颜色，使用 foreground 颜色变量。
            </p>
            <p className="text-muted-foreground">
              这是柔和的文本颜色，使用 muted-foreground 颜色变量，通常用于辅助信息。
            </p>
            <p className="text-primary">
              这是主色调文本，使用 primary 颜色变量，通常用于链接和重要信息。
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 边框和分割线 */}
      <Card>
        <CardHeader>
          <CardTitle>边框和分割线</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="border rounded-lg p-4">
            <p className="text-foreground">带边框的容器</p>
          </div>
          
          <hr className="border-border" />
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="border-l-4 border-primary pl-4">
              <p className="text-foreground font-medium">主色调左边框</p>
              <p className="text-muted-foreground text-sm">用于强调重要内容</p>
            </div>
            
            <div className="border-l-4 border-muted pl-4">
              <p className="text-foreground font-medium">柔和左边框</p>
              <p className="text-muted-foreground text-sm">用于一般内容分组</p>
            </div>
            
            <div className="border-l-4 border-destructive pl-4">
              <p className="text-foreground font-medium">警告左边框</p>
              <p className="text-muted-foreground text-sm">用于错误或警告信息</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 返回按钮 */}
      <div className="mt-12 text-center">
        <Button 
          variant="outline" 
          onClick={() => window.history.back()}
        >
          ← 返回上一页
        </Button>
      </div>
    </div>
  );
}
