import { NextResponse } from 'next/server';
import { generateBlogJSONFeed } from '@/lib/rss';

export async function GET() {
  try {
    const jsonFeed = await generateBlogJSONFeed();
    
    return new NextResponse(jsonFeed, {
      status: 200,
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // 缓存1小时
      },
    });
  } catch (error) {
    console.error('Error generating JSON feed:', error);
    return new NextResponse('Error generating JSON feed', { status: 500 });
  }
}
