import { NextResponse } from 'next/server';
import { generateSitemap } from '@/lib/rss';

export async function GET() {
  try {
    const sitemapXML = await generateSitemap();
    
    return new NextResponse(sitemapXML, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'public, max-age=86400, s-maxage=86400', // 缓存24小时
      },
    });
  } catch (error) {
    console.error('Error generating sitemap:', error);
    return new NextResponse('Error generating sitemap', { status: 500 });
  }
}
