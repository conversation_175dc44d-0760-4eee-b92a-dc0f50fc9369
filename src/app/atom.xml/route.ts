import { NextResponse } from 'next/server';
import { generateBlogAtom } from '@/lib/rss';

export async function GET() {
  try {
    const atomXML = await generateBlogAtom();
    
    return new NextResponse(atomXML, {
      status: 200,
      headers: {
        'Content-Type': 'application/atom+xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // 缓存1小时
      },
    });
  } catch (error) {
    console.error('Error generating Atom feed:', error);
    return new NextResponse('Error generating Atom feed', { status: 500 });
  }
}
