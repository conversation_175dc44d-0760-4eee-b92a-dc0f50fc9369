import { Metadata } from "next";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { SITE_CONFIG, SKILLS_CONFIG } from "@/lib/constants";

export const metadata: Metadata = {
  title: "关于我",
  description: `了解更多关于${SITE_CONFIG.author.name}的信息，包括技能、经历和联系方式`,
};

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/10 py-20">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="max-w-4xl mx-auto text-center">
            {/* Avatar */}
            <div className="relative mb-12">
              <div className="w-40 h-40 mx-auto relative">
                <div className="w-full h-full bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-5xl font-bold text-primary-foreground shadow-2xl">
                  Z
                </div>
                <div className="absolute -inset-6 bg-gradient-to-r from-primary/20 to-transparent rounded-full blur-2xl animate-pulse"></div>
              </div>
            </div>

            {/* Main Content */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight">
              你好，我是
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary/60 block mt-2">
                {SITE_CONFIG.author.name}
              </span>
            </h1>

            <p className="text-xl sm:text-2xl text-muted-foreground mb-8 font-medium">
              全栈开发工程师 • 技术博主 • 摄影爱好者
            </p>

            <p className="text-lg text-muted-foreground leading-relaxed max-w-3xl mx-auto mb-12">
              {SITE_CONFIG.author.bio}
            </p>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">3+</div>
                <div className="text-sm text-muted-foreground">年开发经验</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">50+</div>
                <div className="text-sm text-muted-foreground">项目经验</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">20+</div>
                <div className="text-sm text-muted-foreground">技术文章</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">100+</div>
                <div className="text-sm text-muted-foreground">摄影作品</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">技能专长</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                专注于现代Web开发技术栈，持续学习和实践最新的技术趋势
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {SKILLS_CONFIG.categories.map((category, index) => (
                <Card key={category.name} className="group hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border-0 bg-gradient-to-br from-background to-muted/10">
                  <CardHeader className="pb-4">
                    <div className={`w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform shadow-lg ${
                      index === 0 ? 'bg-gradient-to-br from-blue-500 to-blue-600' :
                      index === 1 ? 'bg-gradient-to-br from-green-500 to-green-600' :
                      'bg-gradient-to-br from-purple-500 to-purple-600'
                    }`}>
                      <span className="text-3xl">
                        {index === 0 ? '💻' : index === 1 ? '🎨' : '🛠️'}
                      </span>
                    </div>
                    <CardTitle className="text-xl text-center mb-4">{category.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {category.skills.map((skill) => (
                        <div key={skill.name} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-foreground">{skill.name}</span>
                            <span className="text-xs text-muted-foreground capitalize">{skill.level}</span>
                          </div>
                          <div className="w-full bg-muted rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all duration-1000 ${
                                index === 0 ? 'bg-gradient-to-r from-blue-500 to-blue-600' :
                                index === 1 ? 'bg-gradient-to-r from-green-500 to-green-600' :
                                'bg-gradient-to-r from-purple-500 to-purple-600'
                              }`}
                              style={{ width: `${getSkillLevel(skill.level) * 25}%` }}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Experience Section */}
      <section className="py-20 bg-muted/20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">工作经历</h2>
              <p className="text-xl text-muted-foreground">
                在不同规模的公司积累了丰富的开发经验
              </p>
            </div>

            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary to-primary/20"></div>

              <div className="space-y-12">
                <div className="relative">
                  {/* Timeline dot */}
                  <div className="absolute left-6 w-4 h-4 bg-primary rounded-full border-4 border-background shadow-lg"></div>

                  <Card className="ml-20 hover:shadow-lg transition-all duration-300">
                    <CardHeader>
                      <div className="flex items-center justify-between mb-2">
                        <CardTitle className="text-xl">全栈开发工程师</CardTitle>
                        <span className="px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-sm rounded-full">
                          当前职位
                        </span>
                      </div>
                      <CardDescription className="text-base">
                        某科技公司 • 2022年3月 - 至今 • 2年9个月
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                          <p className="text-muted-foreground">负责前端React应用的开发和维护，使用Next.js、TypeScript等现代技术栈</p>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                          <p className="text-muted-foreground">参与后端API设计和数据库优化，提升系统性能和用户体验</p>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                          <p className="text-muted-foreground">推动团队采用现代化开发工具和流程，提高开发效率</p>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                          <p className="text-muted-foreground">指导初级开发者，分享技术经验，提升团队整体技术水平</p>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-2 mt-6">
                        <span className="px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 text-xs rounded-full">React</span>
                        <span className="px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 text-xs rounded-full">Next.js</span>
                        <span className="px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 text-xs rounded-full">TypeScript</span>
                        <span className="px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 text-xs rounded-full">Node.js</span>
                        <span className="px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 text-xs rounded-full">PostgreSQL</span>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="relative">
                  {/* Timeline dot */}
                  <div className="absolute left-6 w-4 h-4 bg-muted-foreground rounded-full border-4 border-background shadow-lg"></div>

                  <Card className="ml-20 hover:shadow-lg transition-all duration-300">
                    <CardHeader>
                      <CardTitle className="text-xl">前端开发工程师</CardTitle>
                      <CardDescription className="text-base">
                        某互联网公司 • 2020年6月 - 2022年2月 • 1年8个月
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-muted-foreground rounded-full mt-2 flex-shrink-0"></div>
                          <p className="text-muted-foreground">开发和维护多个Web应用项目，涵盖电商、管理系统等不同领域</p>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-muted-foreground rounded-full mt-2 flex-shrink-0"></div>
                          <p className="text-muted-foreground">优化页面性能，实现懒加载、代码分割等技术，提升用户体验</p>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-muted-foreground rounded-full mt-2 flex-shrink-0"></div>
                          <p className="text-muted-foreground">与设计师和产品经理紧密合作，确保产品设计的完美实现</p>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-muted-foreground rounded-full mt-2 flex-shrink-0"></div>
                          <p className="text-muted-foreground">参与技术选型和架构设计，为项目的长期发展奠定基础</p>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-2 mt-6">
                        <span className="px-3 py-1 bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200 text-xs rounded-full">Vue.js</span>
                        <span className="px-3 py-1 bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200 text-xs rounded-full">JavaScript</span>
                        <span className="px-3 py-1 bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200 text-xs rounded-full">Webpack</span>
                        <span className="px-3 py-1 bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200 text-xs rounded-full">Sass</span>
                        <span className="px-3 py-1 bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200 text-xs rounded-full">Element UI</span>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-gradient-to-br from-primary/5 to-primary/10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6">让我们连接</h2>
            <p className="text-xl text-muted-foreground mb-12 leading-relaxed">
              如果你想与我交流技术、合作项目或者只是想聊聊天，随时欢迎联系我
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              {SITE_CONFIG.author.social.email && (
                <a
                  href={`mailto:${SITE_CONFIG.author.social.email}`}
                  className="group p-6 bg-background rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-border/50"
                >
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                    <span className="text-2xl">📧</span>
                  </div>
                  <div className="text-lg font-semibold text-foreground mb-2">邮箱联系</div>
                  <div className="text-sm text-muted-foreground">发送邮件交流</div>
                </a>
              )}

              {SITE_CONFIG.author.social.github && (
                <a
                  href={SITE_CONFIG.author.social.github}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group p-6 bg-background rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-border/50"
                >
                  <div className="w-16 h-16 bg-gradient-to-br from-gray-700 to-gray-900 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                    <span className="text-2xl">🐙</span>
                  </div>
                  <div className="text-lg font-semibold text-foreground mb-2">GitHub</div>
                  <div className="text-sm text-muted-foreground">查看我的代码</div>
                </a>
              )}

              {SITE_CONFIG.author.social.twitter && (
                <a
                  href={SITE_CONFIG.author.social.twitter}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group p-6 bg-background rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-border/50"
                >
                  <div className="w-16 h-16 bg-gradient-to-br from-sky-400 to-sky-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                    <span className="text-2xl">🐦</span>
                  </div>
                  <div className="text-lg font-semibold text-foreground mb-2">Twitter</div>
                  <div className="text-sm text-muted-foreground">关注我的动态</div>
                </a>
              )}

              {SITE_CONFIG.author.social.linkedin && (
                <a
                  href={SITE_CONFIG.author.social.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group p-6 bg-background rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-border/50"
                >
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                    <span className="text-2xl">💼</span>
                  </div>
                  <div className="text-lg font-semibold text-foreground mb-2">LinkedIn</div>
                  <div className="text-sm text-muted-foreground">专业网络连接</div>
                </a>
              )}
            </div>

            {/* Call to Action */}
            <div className="bg-background/80 backdrop-blur-sm rounded-2xl p-8 border border-border/50">
              <h3 className="text-2xl font-bold text-foreground mb-4">准备开始合作？</h3>
              <p className="text-muted-foreground mb-6">
                无论是技术咨询、项目合作还是摄影服务，我都很乐意与你交流
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="shadow-lg hover:shadow-xl transition-all">
                  <a href={`mailto:${SITE_CONFIG.author.social.email}`}>
                    <span className="mr-2">📧</span>
                    发送邮件
                  </a>
                </Button>
                <Button asChild variant="outline" size="lg" className="shadow-lg hover:shadow-xl transition-all">
                  <Link href="/portfolio">
                    <span className="mr-2">💼</span>
                    查看作品
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

function getSkillLevel(level: string): number {
  switch (level) {
    case "beginner":
      return 1;
    case "intermediate":
      return 2;
    case "advanced":
      return 3;
    case "expert":
      return 4;
    default:
      return 1;
  }
}
