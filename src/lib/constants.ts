import { SiteConfig } from '@/types';

// Site configuration
export const SITE_CONFIG: SiteConfig = {
  name: "<PERSON><PERSON>'s Blog",
  description: "个人博客 - 技术分享、摄影作品、生活记录",
  url: process.env.NEXT_PUBLIC_SITE_URL || "https://zer-blog.vercel.app",
  author: {
    name: "Zer",
    email: "<EMAIL>",
    bio: "全栈开发者，摄影爱好者，热爱技术与生活",
    avatar: "/images/avatar.jpg",
    social: {
      github: "https://github.com/zer",
      twitter: "https://twitter.com/zer",
      linkedin: "https://linkedin.com/in/zer",
      instagram: "https://instagram.com/zer",
      email: "<EMAIL>",
    },
  },
  navigation: [
    { name: "首页", href: "/" },
    { name: "关于", href: "/about" },
    { name: "博客", href: "/blog" },
    { name: "作品集", href: "/portfolio" },
    { name: "摄影", href: "/photography" },
  ],
  seo: {
    title: "Zer's Blog - 技术分享与摄影作品",
    description: "全栈开发者的个人博客，分享技术文章、摄影作品和生活感悟",
    keywords: [
      "前端开发",
      "React",
      "Next.js",
      "TypeScript",
      "摄影",
      "个人博客",
      "技术分享",
    ],
    ogImage: "/images/og-image.jpg",
  },
};

// Blog configuration
export const BLOG_CONFIG = {
  postsPerPage: 10,
  featuredPostsCount: 3,
  recentPostsCount: 5,
  relatedPostsCount: 3,
  excerptLength: 160,
  dateFormat: "YYYY年MM月DD日",
  categories: [
    "前端开发",
    "后端开发",
    "移动开发",
    "DevOps",
    "设计",
    "生活",
    "摄影",
    "其他",
  ],
};

// Photography configuration
export const PHOTOGRAPHY_CONFIG = {
  photosPerPage: 12,
  categories: [
    { id: "landscape", name: "风景", description: "自然风光摄影作品" },
    { id: "portrait", name: "人像", description: "人物肖像摄影作品" },
    { id: "street", name: "街拍", description: "街头摄影作品" },
    { id: "architecture", name: "建筑", description: "建筑摄影作品" },
    { id: "nature", name: "自然", description: "自然生态摄影作品" },
    { id: "travel", name: "旅行", description: "旅行摄影作品" },
  ],
  imageQuality: 85,
  thumbnailSizes: [400, 800, 1200],
  lightboxOptions: {
    showThumbnails: true,
    showCaption: true,
    showCounter: true,
    enableKeyboard: true,
    enableSwipe: true,
  },
};

// Project configuration
export const PROJECT_CONFIG = {
  projectsPerPage: 6,
  featuredProjectsCount: 3,
  technologies: [
    "React",
    "Next.js",
    "TypeScript",
    "Node.js",
    "Python",
    "Django",
    "PostgreSQL",
    "MongoDB",
    "Redis",
    "Docker",
    "AWS",
    "Vercel",
    "TailwindCSS",
    "Framer Motion",
  ],
  categories: [
    "Web应用",
    "移动应用",
    "桌面应用",
    "API服务",
    "工具库",
    "开源项目",
  ],
};

// Skills configuration
export const SKILLS_CONFIG = {
  categories: [
    {
      name: "前端开发",
      skills: [
        { name: "React", level: "expert" as const },
        { name: "Next.js", level: "expert" as const },
        { name: "TypeScript", level: "advanced" as const },
        { name: "JavaScript", level: "expert" as const },
        { name: "HTML/CSS", level: "expert" as const },
        { name: "TailwindCSS", level: "advanced" as const },
        { name: "Framer Motion", level: "intermediate" as const },
      ],
    },
    {
      name: "后端开发",
      skills: [
        { name: "Node.js", level: "advanced" as const },
        { name: "Python", level: "advanced" as const },
        { name: "Django", level: "intermediate" as const },
        { name: "PostgreSQL", level: "advanced" as const },
        { name: "MongoDB", level: "intermediate" as const },
        { name: "Redis", level: "intermediate" as const },
      ],
    },
    {
      name: "DevOps & 工具",
      skills: [
        { name: "Docker", level: "intermediate" as const },
        { name: "AWS", level: "intermediate" as const },
        { name: "Vercel", level: "advanced" as const },
        { name: "Git", level: "expert" as const },
        { name: "Linux", level: "advanced" as const },
      ],
    },
  ],
};

// Theme configuration
export const THEME_CONFIG = {
  defaultTheme: "system" as const,
  storageKey: "zer-blog-theme",
  enableSystemTheme: true,
  enableTransitions: true,
};

// Animation configuration
export const ANIMATION_CONFIG = {
  pageTransition: {
    duration: 0.3,
    ease: "easeInOut",
  },
  stagger: {
    children: 0.1,
    delayChildren: 0.2,
  },
  fadeIn: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.5 },
  },
  slideUp: {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.4 },
  },
};

// SEO configuration
export const SEO_CONFIG = {
  defaultTitle: SITE_CONFIG.seo.title,
  titleTemplate: "%s | Zer's Blog",
  defaultDescription: SITE_CONFIG.seo.description,
  siteUrl: SITE_CONFIG.url,
  defaultImage: SITE_CONFIG.seo.ogImage,
  twitterHandle: "@zer",
  locale: "zh-CN",
  type: "website",
};

// Contact configuration
export const CONTACT_CONFIG = {
  email: SITE_CONFIG.author.email,
  social: SITE_CONFIG.author.social,
  formEndpoint: "/api/contact",
  enableContactForm: true,
  enableSocialLinks: true,
};

// Performance configuration
export const PERFORMANCE_CONFIG = {
  imageOptimization: {
    quality: 85,
    formats: ["webp", "avif"],
    sizes: [640, 768, 1024, 1280, 1920],
  },
  caching: {
    staticAssets: "1y",
    pages: "1h",
    api: "5m",
  },
  lazyLoading: {
    rootMargin: "50px",
    threshold: 0.1,
  },
};

// Development configuration
export const DEV_CONFIG = {
  showDebugInfo: process.env.NODE_ENV === "development",
  enableHotReload: process.env.NODE_ENV === "development",
  logLevel: process.env.NODE_ENV === "development" ? "debug" : "error",
};
