// Blog post types
export interface BlogPost {
  slug: string;
  title: string;
  description: string;
  content: string;
  date: string;
  tags: string[];
  category?: string;
  author: string;
  readingTime: number;
  featured?: boolean;
  coverImage?: string;
}

export interface BlogPostMeta {
  slug: string;
  title: string;
  description: string;
  date: string;
  tags: string[];
  category?: string;
  author: string;
  readingTime: number;
  featured?: boolean;
  coverImage?: string;
}

// Photography types
export interface PhotoCategory {
  id: string;
  name: string;
  description: string;
  coverImage: string;
}

export interface Photo {
  id: string;
  title: string;
  description?: string;
  src: string;
  alt: string;
  category: string;
  tags: string[];
  date: string;
  camera?: string;
  lens?: string;
  settings?: {
    aperture?: string;
    shutter?: string;
    iso?: string;
    focalLength?: string;
  };
  location?: string;
  featured?: boolean;
}

// Navigation types
export interface NavItem {
  name: string;
  href: string;
  icon?: string;
  external?: boolean;
}

// Site configuration types
export interface SiteConfig {
  name: string;
  description: string;
  url: string;
  author: {
    name: string;
    email: string;
    bio: string;
    avatar: string;
    social: {
      github?: string;
      twitter?: string;
      linkedin?: string;
      instagram?: string;
      email?: string;
    };
  };
  navigation: NavItem[];
  seo: {
    title: string;
    description: string;
    keywords: string[];
    ogImage: string;
  };
}

// Project types
export interface Project {
  id: string;
  title: string;
  description: string;
  longDescription?: string;
  image: string;
  tags: string[];
  technologies: string[];
  links: {
    demo?: string;
    github?: string;
    website?: string;
  };
  featured?: boolean;
  status: 'completed' | 'in-progress' | 'planned';
  date: string;
}

// Skill types
export interface Skill {
  name: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  category: 'frontend' | 'backend' | 'mobile' | 'devops' | 'design' | 'other';
  icon?: string;
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Common component props
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Search types
export interface SearchResult {
  type: 'blog' | 'project' | 'photo';
  title: string;
  description: string;
  url: string;
  date?: string;
  tags?: string[];
}

// Pagination types
export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  basePath: string;
}

// Form types
export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Metadata types for SEO
export interface PageMetadata {
  title: string;
  description: string;
  keywords?: string[];
  ogImage?: string;
  ogType?: string;
  canonicalUrl?: string;
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  tags?: string[];
}
