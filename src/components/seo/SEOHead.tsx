import { Metadata } from 'next';
import { SITE_CONFIG, SEO_CONFIG } from '@/lib/constants';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tags?: string[];
  noIndex?: boolean;
  noFollow?: boolean;
}

/**
 * 生成页面的 SEO 元数据
 */
export function generateSEOMetadata({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  section,
  tags = [],
  noIndex = false,
  noFollow = false,
}: SEOProps = {}): Metadata {
  const seoTitle = title 
    ? `${title} | ${SITE_CONFIG.name}`
    : SEO_CONFIG.defaultTitle;
  
  const seoDescription = description || SEO_CONFIG.defaultDescription;
  const seoImage = image || SEO_CONFIG.defaultImage;
  const seoUrl = url ? `${SITE_CONFIG.url}${url}` : SITE_CONFIG.url;
  
  // 合并关键词
  const allKeywords = [
    ...SITE_CONFIG.seo.keywords,
    ...keywords,
    ...tags,
  ].filter((keyword, index, array) => array.indexOf(keyword) === index);

  // 构建 robots 指令
  const robotsDirectives = [];
  if (noIndex) robotsDirectives.push('noindex');
  if (noFollow) robotsDirectives.push('nofollow');
  if (robotsDirectives.length === 0) {
    robotsDirectives.push('index', 'follow');
  }

  const metadata: Metadata = {
    title: seoTitle,
    description: seoDescription,
    keywords: allKeywords,
    authors: [{ name: author || SITE_CONFIG.author.name, url: SITE_CONFIG.url }],
    creator: SITE_CONFIG.author.name,
    publisher: SITE_CONFIG.author.name,
    robots: robotsDirectives.join(', '),
    
    // Open Graph
    openGraph: {
      type: type as any,
      locale: SEO_CONFIG.locale,
      url: seoUrl,
      title: seoTitle,
      description: seoDescription,
      siteName: SITE_CONFIG.name,
      images: [
        {
          url: seoImage,
          width: 1200,
          height: 630,
          alt: title || SITE_CONFIG.name,
        },
      ],
      ...(type === 'article' && {
        publishedTime,
        modifiedTime,
        authors: [author || SITE_CONFIG.author.name],
        section,
        tags,
      }),
    },

    // Twitter Card
    twitter: {
      card: 'summary_large_image',
      title: seoTitle,
      description: seoDescription,
      images: [seoImage],
      creator: SEO_CONFIG.twitterHandle,
      site: SEO_CONFIG.twitterHandle,
    },

    // 其他元数据
    metadataBase: new URL(SITE_CONFIG.url),
    alternates: {
      canonical: seoUrl,
      types: {
        'application/rss+xml': '/rss.xml',
        'application/atom+xml': '/atom.xml',
        'application/json': '/feed.json',
      },
    },
  };

  return metadata;
}

/**
 * 生成博客文章的 SEO 元数据
 */
export function generateBlogPostSEO({
  title,
  description,
  slug,
  date,
  tags = [],
  author,
  image,
}: {
  title: string;
  description?: string;
  slug: string;
  date: string;
  tags?: string[];
  author?: string;
  image?: string;
}): Metadata {
  return generateSEOMetadata({
    title,
    description,
    url: `/blog/${slug}`,
    type: 'article',
    publishedTime: new Date(date).toISOString(),
    modifiedTime: new Date(date).toISOString(),
    author: author || SITE_CONFIG.author.name,
    section: 'Technology',
    tags,
    keywords: tags,
    image: image || SEO_CONFIG.defaultImage,
  });
}

/**
 * 生成摄影作品的 SEO 元数据
 */
export function generatePhotoSEO({
  title,
  description,
  id,
  category,
  tags = [],
  image,
}: {
  title: string;
  description?: string;
  id: string;
  category?: string;
  tags?: string[];
  image: string;
}): Metadata {
  return generateSEOMetadata({
    title,
    description: description || `${SITE_CONFIG.author.name}的摄影作品 - ${title}`,
    url: `/photography/${id}`,
    type: 'article',
    section: category || 'Photography',
    tags: ['摄影', '作品集', ...(category ? [category] : []), ...tags],
    keywords: ['摄影', '作品集', ...(category ? [category] : []), ...tags],
    image,
  });
}

/**
 * 生成项目作品的 SEO 元数据
 */
export function generateProjectSEO({
  title,
  description,
  slug,
  technologies = [],
  category,
  image,
}: {
  title: string;
  description: string;
  slug: string;
  technologies?: string[];
  category?: string;
  image?: string;
}): Metadata {
  return generateSEOMetadata({
    title,
    description,
    url: `/portfolio/${slug}`,
    type: 'article',
    section: category || 'Projects',
    tags: ['项目', '作品集', ...(category ? [category] : []), ...technologies],
    keywords: ['项目', '作品集', ...(category ? [category] : []), ...technologies],
    image: image || SEO_CONFIG.defaultImage,
  });
}

/**
 * 生成标签页面的 SEO 元数据
 */
export function generateTagSEO(tag: string, type: 'blog' | 'photography' = 'blog'): Metadata {
  const typeMap = {
    blog: '博客',
    photography: '摄影',
  };

  return generateSEOMetadata({
    title: `${tag} - ${typeMap[type]}标签`,
    description: `浏览所有标记为"${tag}"的${typeMap[type]}内容`,
    url: `/${type}/tag/${encodeURIComponent(tag)}`,
    keywords: [tag, typeMap[type], '标签', '分类'],
  });
}

/**
 * 生成分类页面的 SEO 元数据
 */
export function generateCategorySEO(
  category: string, 
  type: 'blog' | 'photography' | 'portfolio' = 'blog'
): Metadata {
  const typeMap = {
    blog: '博客',
    photography: '摄影',
    portfolio: '项目',
  };

  return generateSEOMetadata({
    title: `${category} - ${typeMap[type]}分类`,
    description: `浏览${category}分类下的所有${typeMap[type]}内容`,
    url: `/${type}/category/${encodeURIComponent(category)}`,
    keywords: [category, typeMap[type], '分类', '目录'],
  });
}

/**
 * 生成搜索页面的 SEO 元数据
 */
export function generateSearchSEO(
  query: string, 
  type: 'blog' | 'photography' | 'portfolio' = 'blog'
): Metadata {
  const typeMap = {
    blog: '博客',
    photography: '摄影',
    portfolio: '项目',
  };

  return generateSEOMetadata({
    title: `搜索"${query}" - ${typeMap[type]}`,
    description: `搜索包含"${query}"的${typeMap[type]}内容`,
    url: `/${type}/search?q=${encodeURIComponent(query)}`,
    keywords: [query, '搜索', typeMap[type]],
    noIndex: true, // 搜索页面通常不需要被索引
  });
}

/**
 * JSON-LD 结构化数据生成器
 */
export function generateJSONLD(data: any) {
  return {
    __html: JSON.stringify({
      '@context': 'https://schema.org',
      ...data,
    }),
  };
}

/**
 * 生成网站的结构化数据
 */
export function generateWebsiteJSONLD() {
  return generateJSONLD({
    '@type': 'WebSite',
    name: SITE_CONFIG.name,
    description: SITE_CONFIG.description,
    url: SITE_CONFIG.url,
    author: {
      '@type': 'Person',
      name: SITE_CONFIG.author.name,
      email: SITE_CONFIG.author.email,
      url: SITE_CONFIG.url,
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: `${SITE_CONFIG.url}/blog/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  });
}

/**
 * 生成博客文章的结构化数据
 */
export function generateArticleJSONLD({
  title,
  description,
  slug,
  date,
  author,
  image,
  tags = [],
}: {
  title: string;
  description: string;
  slug: string;
  date: string;
  author?: string;
  image?: string;
  tags?: string[];
}) {
  return generateJSONLD({
    '@type': 'BlogPosting',
    headline: title,
    description,
    url: `${SITE_CONFIG.url}/blog/${slug}`,
    datePublished: new Date(date).toISOString(),
    dateModified: new Date(date).toISOString(),
    author: {
      '@type': 'Person',
      name: author || SITE_CONFIG.author.name,
      email: SITE_CONFIG.author.email,
    },
    publisher: {
      '@type': 'Organization',
      name: SITE_CONFIG.name,
      url: SITE_CONFIG.url,
    },
    ...(image && {
      image: {
        '@type': 'ImageObject',
        url: image,
        width: 1200,
        height: 630,
      },
    }),
    keywords: tags.join(', '),
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${SITE_CONFIG.url}/blog/${slug}`,
    },
  });
}
