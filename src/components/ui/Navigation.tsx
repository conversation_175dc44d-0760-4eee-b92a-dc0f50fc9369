"use client";

import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "./Button";
import { SITE_CONFIG } from "@/lib/constants";

interface NavigationProps {
  className?: string;
}

const Navigation: React.FC<NavigationProps> = ({ className }) => {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <nav className={cn("relative", className)}>
      {/* Desktop Navigation */}
      <div className="hidden md:flex items-center space-x-8">
        {SITE_CONFIG.navigation.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "text-sm font-medium transition-colors hover:text-primary",
              pathname === item.href
                ? "text-primary"
                : "text-muted-foreground"
            )}
          >
            {item.name}
          </Link>
        ))}
      </div>

      {/* Mobile Navigation Button */}
      <div className="md:hidden">
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleMobileMenu}
          aria-label="Toggle mobile menu"
          className="relative z-50"
        >
          <div className="flex flex-col items-center justify-center w-6 h-6">
            <span
              className={cn(
                "block h-0.5 w-6 bg-current transition-all duration-300",
                isMobileMenuOpen ? "rotate-45 translate-y-1" : "-translate-y-1"
              )}
            />
            <span
              className={cn(
                "block h-0.5 w-6 bg-current transition-all duration-300",
                isMobileMenuOpen ? "opacity-0" : "opacity-100"
              )}
            />
            <span
              className={cn(
                "block h-0.5 w-6 bg-current transition-all duration-300",
                isMobileMenuOpen ? "-rotate-45 -translate-y-1" : "translate-y-1"
              )}
            />
          </div>
        </Button>
      </div>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-40 md:hidden"
            onClick={closeMobileMenu}
          />
          
          {/* Menu */}
          <div className="fixed top-0 right-0 h-full w-64 bg-background border-l shadow-lg z-50 md:hidden">
            <div className="flex flex-col p-6 pt-20">
              {SITE_CONFIG.navigation.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={closeMobileMenu}
                  className={cn(
                    "block py-3 px-4 text-sm font-medium transition-colors hover:text-primary hover:bg-accent rounded-md",
                    pathname === item.href
                      ? "text-primary bg-accent"
                      : "text-muted-foreground"
                  )}
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
        </>
      )}
    </nav>
  );
};

export { Navigation };
