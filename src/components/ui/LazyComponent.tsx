"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface LazyComponentProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
  rootMargin?: string;
  threshold?: number;
  triggerOnce?: boolean;
}

/**
 * 懒加载组件包装器
 * 使用 Intersection Observer 实现组件的懒加载
 */
export const LazyComponent: React.FC<LazyComponentProps> = ({
  children,
  fallback = <div className="animate-pulse bg-muted h-32 rounded" />,
  className,
  rootMargin = "50px",
  threshold = 0.1,
  triggerOnce = true,
}) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const [hasTriggered, setHasTriggered] = React.useState(false);
  const elementRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          if (triggerOnce) {
            setHasTriggered(true);
            observer.unobserve(element);
          }
        } else if (!triggerOnce && !hasTriggered) {
          setIsVisible(false);
        }
      },
      {
        rootMargin,
        threshold,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [rootMargin, threshold, triggerOnce, hasTriggered]);

  return (
    <div ref={elementRef} className={cn("min-h-[1px]", className)}>
      {isVisible ? children : fallback}
    </div>
  );
};

/**
 * 动态导入组件的高阶组件
 */
export function withLazyLoading<P extends object>(
  importFunc: () => Promise<{ default: React.ComponentType<P> }>,
  fallback?: React.ReactNode
) {
  const LazyLoadedComponent = React.lazy(importFunc);

  return React.forwardRef<any, P>((props, ref) => (
    <React.Suspense
      fallback={
        fallback || (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )
      }
    >
      <LazyLoadedComponent {...props} ref={ref} />
    </React.Suspense>
  ));
}

/**
 * 代码分割钩子
 */
export function useCodeSplitting<T>(
  importFunc: () => Promise<T>,
  deps: React.DependencyList = []
) {
  const [module, setModule] = React.useState<T | null>(null);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    let cancelled = false;

    const loadModule = async () => {
      try {
        setLoading(true);
        setError(null);
        const loadedModule = await importFunc();
        
        if (!cancelled) {
          setModule(loadedModule);
        }
      } catch (err) {
        if (!cancelled) {
          setError(err instanceof Error ? err : new Error('Failed to load module'));
        }
      } finally {
        if (!cancelled) {
          setLoading(false);
        }
      }
    };

    loadModule();

    return () => {
      cancelled = true;
    };
  }, deps);

  return { module, loading, error };
}

/**
 * 预加载组件
 */
export const PreloadComponent: React.FC<{
  importFunc: () => Promise<any>;
  trigger?: 'hover' | 'visible' | 'immediate';
  children: React.ReactNode;
}> = ({ importFunc, trigger = 'hover', children }) => {
  const [preloaded, setPreloaded] = React.useState(false);
  const elementRef = React.useRef<HTMLDivElement>(null);

  const preload = React.useCallback(async () => {
    if (preloaded) return;
    
    try {
      await importFunc();
      setPreloaded(true);
      
      if (process.env.NODE_ENV === 'development') {
        console.log('Component preloaded successfully');
      }
    } catch (error) {
      console.error('Failed to preload component:', error);
    }
  }, [importFunc, preloaded]);

  React.useEffect(() => {
    if (trigger === 'immediate') {
      preload();
    } else if (trigger === 'visible') {
      const element = elementRef.current;
      if (!element) return;

      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            preload();
            observer.unobserve(element);
          }
        },
        { rootMargin: '100px' }
      );

      observer.observe(element);

      return () => {
        observer.unobserve(element);
      };
    }
  }, [trigger, preload]);

  const handleMouseEnter = trigger === 'hover' ? preload : undefined;

  return (
    <div ref={elementRef} onMouseEnter={handleMouseEnter}>
      {children}
    </div>
  );
};

/**
 * 性能优化的图片懒加载组件
 */
export const LazyImage: React.FC<{
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  quality?: number;
}> = ({ src, alt, className, width, height, quality = 85 }) => {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [isVisible, setIsVisible] = React.useState(false);
  const imgRef = React.useRef<HTMLImageElement>(null);

  React.useEffect(() => {
    const img = imgRef.current;
    if (!img) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(img);
        }
      },
      { rootMargin: '50px' }
    );

    observer.observe(img);

    return () => {
      observer.unobserve(img);
    };
  }, []);

  return (
    <div
      ref={imgRef}
      className={cn("relative overflow-hidden bg-muted", className)}
      style={{ width, height }}
    >
      {isVisible && (
        <img
          src={src}
          alt={alt}
          className={cn(
            "transition-opacity duration-300",
            isLoaded ? "opacity-100" : "opacity-0"
          )}
          onLoad={() => setIsLoaded(true)}
          loading="lazy"
          style={{ width: '100%', height: '100%', objectFit: 'cover' }}
        />
      )}
      
      {!isLoaded && isVisible && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      )}
      
      {!isVisible && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted">
          <div className="text-4xl opacity-50">📷</div>
        </div>
      )}
    </div>
  );
};

// 导出类型
export type { LazyComponentProps };
