import * as React from "react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { SITE_CONFIG } from "@/lib/constants";

interface FooterProps {
  className?: string;
}

const Footer: React.FC<FooterProps> = ({ className }) => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className={cn("border-t bg-background", className)}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="md:col-span-2">
            <Link
              href="/"
              className="flex items-center space-x-2 text-xl font-bold text-foreground hover:text-primary transition-colors mb-4"
            >
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center text-primary-foreground font-bold text-sm">
                Z
              </div>
              <span>{SITE_CONFIG.name}</span>
            </Link>
            <p className="text-muted-foreground text-sm max-w-md">
              {SITE_CONFIG.description}
            </p>
            <p className="text-muted-foreground text-sm mt-2">
              {SITE_CONFIG.author.bio}
            </p>
          </div>

          {/* Navigation */}
          <div>
            <h3 className="font-semibold text-foreground mb-4">导航</h3>
            <ul className="space-y-2">
              {SITE_CONFIG.navigation.map((item) => (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    className="text-sm text-muted-foreground hover:text-primary transition-colors"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Social Links */}
          <div>
            <h3 className="font-semibold text-foreground mb-4">联系方式</h3>
            <ul className="space-y-2">
              {SITE_CONFIG.author.social.github && (
                <li>
                  <a
                    href={SITE_CONFIG.author.social.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center space-x-2"
                  >
                    <span>GitHub</span>
                    <span className="text-xs">↗</span>
                  </a>
                </li>
              )}
              {SITE_CONFIG.author.social.twitter && (
                <li>
                  <a
                    href={SITE_CONFIG.author.social.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center space-x-2"
                  >
                    <span>Twitter</span>
                    <span className="text-xs">↗</span>
                  </a>
                </li>
              )}
              {SITE_CONFIG.author.social.linkedin && (
                <li>
                  <a
                    href={SITE_CONFIG.author.social.linkedin}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center space-x-2"
                  >
                    <span>LinkedIn</span>
                    <span className="text-xs">↗</span>
                  </a>
                </li>
              )}
              {SITE_CONFIG.author.social.email && (
                <li>
                  <a
                    href={`mailto:${SITE_CONFIG.author.social.email}`}
                    className="text-sm text-muted-foreground hover:text-primary transition-colors"
                  >
                    邮箱联系
                  </a>
                </li>
              )}
            </ul>
          </div>
        </div>

        {/* Bottom */}
        <div className="border-t mt-8 pt-8 flex flex-col sm:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground">
            © {currentYear} {SITE_CONFIG.author.name}. All rights reserved.
          </p>
          <div className="flex items-center space-x-4 mt-4 sm:mt-0">
            <Link
              href="/privacy"
              className="text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              隐私政策
            </Link>
            <Link
              href="/terms"
              className="text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              使用条款
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export { Footer };
