"use client";

import * as React from "react";
import { PhotoGrid } from "@/components/photography/PhotoGrid";
import { Lightbox, useLightbox } from "@/components/photography/Lightbox";
import { Photo } from "@/types";

interface PhotoCategoryClientProps {
  photos: Photo[];
}

export const PhotoCategoryClient: React.FC<PhotoCategoryClientProps> = ({ photos }) => {
  const { isOpen, currentIndex, openLightbox, closeLightbox, goToNext, goToPrevious } = useLightbox(photos);

  return (
    <>
      {/* 照片网格 */}
      <PhotoGrid
        photos={photos}
        onPhotoClick={openLightbox}
        columns={4}
        showInfo={true}
        loading={false}
      />

      {/* Lightbox 组件 */}
      <Lightbox
        photos={photos}
        currentIndex={currentIndex}
        isOpen={isOpen}
        onClose={closeLightbox}
        onNext={goToNext}
        onPrevious={goToPrevious}
        showThumbnails={true}
        showCaption={true}
        showCounter={true}
      />
    </>
  );
};
