"use client";

import * as React from "react";
import { PhotoCategory } from "@/types";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/Button";

interface CategoryFilterProps {
  categories: PhotoCategory[];
  activeCategory: string | null;
  onCategoryChange: (categoryId: string | null) => void;
  photoCounts?: Record<string, number>;
  className?: string;
}

export const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  activeCategory,
  onCategoryChange,
  photoCounts = {},
  className,
}) => {
  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {/* 全部分类按钮 */}
      <Button
        variant={activeCategory === null ? "default" : "outline"}
        size="sm"
        onClick={() => onCategoryChange(null)}
        className="transition-all duration-200"
      >
        全部
        {photoCounts.total && (
          <span className="ml-2 text-xs opacity-70">
            ({photoCounts.total})
          </span>
        )}
      </Button>

      {/* 分类按钮 */}
      {categories.map((category) => (
        <Button
          key={category.id}
          variant={activeCategory === category.id ? "default" : "outline"}
          size="sm"
          onClick={() => onCategoryChange(category.id)}
          className="transition-all duration-200"
        >
          {category.name}
          {photoCounts[category.id] && (
            <span className="ml-2 text-xs opacity-70">
              ({photoCounts[category.id]})
            </span>
          )}
        </Button>
      ))}
    </div>
  );
};

// 分类卡片组件
interface CategoryCardProps {
  category: PhotoCategory;
  photoCount: number;
  onClick: () => void;
  className?: string;
}

export const CategoryCard: React.FC<CategoryCardProps> = ({
  category,
  photoCount,
  onClick,
  className,
}) => {
  return (
    <div
      className={cn(
        "group cursor-pointer rounded-lg overflow-hidden bg-card border hover:shadow-lg transition-all duration-300 hover:-translate-y-1",
        className
      )}
      onClick={onClick}
    >
      {/* 分类封面图 */}
      <div className="aspect-[4/3] bg-muted relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
        <div className="absolute inset-0 flex items-center justify-center text-6xl">
          {getCategoryIcon(category.id)}
        </div>
        
        {/* 分类信息覆盖层 */}
        <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
          <h3 className="text-lg font-semibold mb-1">{category.name}</h3>
          <p className="text-sm opacity-90 line-clamp-2">{category.description}</p>
        </div>

        {/* 照片数量标识 */}
        <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded-full">
          {photoCount} 张
        </div>
      </div>
    </div>
  );
};

// 获取分类图标
function getCategoryIcon(categoryId: string): string {
  const icons: Record<string, string> = {
    landscape: "🏔️",
    portrait: "👤",
    street: "🏙️",
    architecture: "🏢",
    nature: "🌿",
    travel: "✈️",
  };
  return icons[categoryId] || "📷";
}

// 分类网格组件
interface CategoryGridProps {
  categories: PhotoCategory[];
  photoCounts: Record<string, number>;
  onCategoryClick: (categoryId: string) => void;
  className?: string;
}

export const CategoryGrid: React.FC<CategoryGridProps> = ({
  categories,
  photoCounts,
  onCategoryClick,
  className,
}) => {
  return (
    <div className={cn("grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6", className)}>
      {categories.map((category) => (
        <CategoryCard
          key={category.id}
          category={category}
          photoCount={photoCounts[category.id] || 0}
          onClick={() => onCategoryClick(category.id)}
        />
      ))}
    </div>
  );
};

// 分类统计组件
interface CategoryStatsProps {
  categories: PhotoCategory[];
  photoCounts: Record<string, number>;
  totalPhotos: number;
  className?: string;
}

export const CategoryStats: React.FC<CategoryStatsProps> = ({
  categories,
  photoCounts,
  totalPhotos,
  className,
}) => {
  return (
    <div className={cn("bg-muted/30 rounded-lg p-6", className)}>
      <h3 className="text-lg font-semibold text-foreground mb-4">分类统计</h3>
      
      <div className="space-y-3">
        {/* 总计 */}
        <div className="flex items-center justify-between py-2 border-b border-border">
          <span className="font-medium text-foreground">总计</span>
          <span className="text-muted-foreground">{totalPhotos} 张</span>
        </div>

        {/* 各分类统计 */}
        {categories.map((category) => {
          const count = photoCounts[category.id] || 0;
          const percentage = totalPhotos > 0 ? (count / totalPhotos * 100).toFixed(1) : '0';
          
          return (
            <div key={category.id} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-lg">{getCategoryIcon(category.id)}</span>
                <span className="text-sm text-foreground">{category.name}</span>
              </div>
              <div className="text-right">
                <div className="text-sm text-muted-foreground">{count} 张</div>
                <div className="text-xs text-muted-foreground">{percentage}%</div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// 分类选择器 Hook
export const useCategoryFilter = (initialCategory: string | null = null) => {
  const [activeCategory, setActiveCategory] = React.useState<string | null>(initialCategory);

  const handleCategoryChange = React.useCallback((categoryId: string | null) => {
    setActiveCategory(categoryId);
  }, []);

  const resetCategory = React.useCallback(() => {
    setActiveCategory(null);
  }, []);

  return {
    activeCategory,
    handleCategoryChange,
    resetCategory,
  };
};
