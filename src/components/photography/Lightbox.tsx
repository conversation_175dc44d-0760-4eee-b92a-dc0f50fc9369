"use client";

import * as React from "react";
import { Photo } from "@/types";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/Button";
import { Image } from "@/components/ui/Image";
import { formatPhotoDate } from "@/lib/photography";

interface LightboxProps {
  photos: Photo[];
  currentIndex: number;
  isOpen: boolean;
  onClose: () => void;
  onNext: () => void;
  onPrevious: () => void;
  showThumbnails?: boolean;
  showCaption?: boolean;
  showCounter?: boolean;
}

export const Lightbox: React.FC<LightboxProps> = ({
  photos,
  currentIndex,
  isOpen,
  onClose,
  onNext,
  onPrevious,
  showThumbnails = true,
  showCaption = true,
  showCounter = true,
}) => {
  const currentPhoto = photos[currentIndex];
  const [isLoading, setIsLoading] = React.useState(true);

  // 键盘事件处理
  React.useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          onPrevious();
          break;
        case 'ArrowRight':
          onNext();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose, onNext, onPrevious]);

  // 防止背景滚动
  React.useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // 预加载相邻图片
  React.useEffect(() => {
    if (!isOpen || !currentPhoto) return;

    const preloadImage = (src: string) => {
      const img = new window.Image();
      img.src = src;
    };

    // 预加载前一张和后一张图片
    if (currentIndex > 0) {
      preloadImage(photos[currentIndex - 1].src);
    }
    if (currentIndex < photos.length - 1) {
      preloadImage(photos[currentIndex + 1].src);
    }
  }, [currentIndex, photos, isOpen, currentPhoto]);

  if (!isOpen || !currentPhoto) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/90 backdrop-blur-sm">
      {/* 关闭按钮 */}
      <Button
        variant="ghost"
        size="icon"
        className="absolute top-4 right-4 z-10 text-white hover:bg-white/20"
        onClick={onClose}
      >
        <span className="text-2xl">×</span>
      </Button>

      {/* 计数器 */}
      {showCounter && (
        <div className="absolute top-4 left-4 z-10 text-white text-sm bg-black/50 px-3 py-1 rounded-full">
          {currentIndex + 1} / {photos.length}
        </div>
      )}

      {/* 导航按钮 */}
      {photos.length > 1 && (
        <>
          <Button
            variant="ghost"
            size="icon"
            className="absolute left-4 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20 disabled:opacity-30"
            onClick={onPrevious}
            disabled={currentIndex === 0}
          >
            <span className="text-2xl">‹</span>
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20 disabled:opacity-30"
            onClick={onNext}
            disabled={currentIndex === photos.length - 1}
          >
            <span className="text-2xl">›</span>
          </Button>
        </>
      )}

      {/* 主图片区域 */}
      <div className="flex items-center justify-center h-full p-4">
        <div className="relative max-w-7xl max-h-full">
          <Image
            src={currentPhoto.src}
            alt={currentPhoto.alt}
            width={1200}
            height={800}
            className="max-w-full max-h-full object-contain"
            priority
            onLoad={() => setIsLoading(false)}
          />
          
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
            </div>
          )}
        </div>
      </div>

      {/* 图片信息 */}
      {showCaption && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
          <div className="max-w-4xl mx-auto text-white">
            <h3 className="text-xl font-semibold mb-2">{currentPhoto.title}</h3>
            {currentPhoto.description && (
              <p className="text-gray-300 mb-3">{currentPhoto.description}</p>
            )}
            
            <div className="flex flex-wrap gap-4 text-sm text-gray-400">
              {currentPhoto.camera && (
                <span>📷 {currentPhoto.camera}</span>
              )}
              {currentPhoto.lens && (
                <span>🔍 {currentPhoto.lens}</span>
              )}
              {currentPhoto.settings && (
                <span>
                  ⚙️ {currentPhoto.settings.aperture} • {currentPhoto.settings.shutter} • ISO {currentPhoto.settings.iso}
                </span>
              )}
              {currentPhoto.location && (
                <span>📍 {currentPhoto.location}</span>
              )}
              <span>📅 {formatPhotoDate(currentPhoto.date)}</span>
            </div>

            {currentPhoto.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-3">
                {currentPhoto.tags.map((tag) => (
                  <span
                    key={tag}
                    className="px-2 py-1 bg-white/20 text-white text-xs rounded-md"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* 缩略图导航 */}
      {showThumbnails && photos.length > 1 && (
        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 max-w-4xl">
          <div className="flex gap-2 overflow-x-auto scrollbar-hide p-2 bg-black/50 rounded-lg">
            {photos.map((photo, index) => (
              <button
                key={photo.id}
                className={cn(
                  "flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border-2 transition-all",
                  index === currentIndex
                    ? "border-white scale-110"
                    : "border-transparent hover:border-white/50"
                )}
                onClick={() => {
                  // 这里需要父组件提供跳转到指定索引的方法
                  // 暂时使用连续的 next/previous 调用
                  const diff = index - currentIndex;
                  if (diff > 0) {
                    for (let i = 0; i < diff; i++) {
                      onNext();
                    }
                  } else if (diff < 0) {
                    for (let i = 0; i < Math.abs(diff); i++) {
                      onPrevious();
                    }
                  }
                }}
              >
                <Image
                  src={photo.src}
                  alt={photo.alt}
                  width={64}
                  height={64}
                  className="w-full h-full object-cover"
                />
              </button>
            ))}
          </div>
        </div>
      )}

      {/* 点击背景关闭 */}
      <div
        className="absolute inset-0 -z-10"
        onClick={onClose}
      />
    </div>
  );
};

// Lightbox Hook
export const useLightbox = (photos: Photo[]) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [currentIndex, setCurrentIndex] = React.useState(0);

  const openLightbox = (index: number) => {
    setCurrentIndex(index);
    setIsOpen(true);
  };

  const closeLightbox = () => {
    setIsOpen(false);
  };

  const goToNext = () => {
    setCurrentIndex((prev) => (prev + 1) % photos.length);
  };

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev - 1 + photos.length) % photos.length);
  };

  const goToIndex = (index: number) => {
    setCurrentIndex(index);
  };

  return {
    isOpen,
    currentIndex,
    openLightbox,
    closeLightbox,
    goToNext,
    goToPrevious,
    goToIndex,
  };
};
