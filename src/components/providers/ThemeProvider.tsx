"use client";

import * as React from "react";
import { THEME_CONFIG } from "@/lib/constants";

type Theme = "light" | "dark" | "system";

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  resolvedTheme: "light" | "dark";
  systemTheme: "light" | "dark";
}

const ThemeContext = React.createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
  enableSystem?: boolean;
  disableTransitionOnChange?: boolean;
}

export function ThemeProvider({
  children,
  defaultTheme = THEME_CONFIG.defaultTheme,
  storageKey = THEME_CONFIG.storageKey,
  enableSystem = THEME_CONFIG.enableSystemTheme,
  disableTransitionOnChange = false,
}: ThemeProviderProps) {
  const [theme, setThemeState] = React.useState<Theme>(defaultTheme);
  const [systemTheme, setSystemTheme] = React.useState<"light" | "dark">("light");
  const [mounted, setMounted] = React.useState(false);

  // Get resolved theme (what's actually being displayed)
  const resolvedTheme = theme === "system" ? systemTheme : theme;

  // Load theme from localStorage on mount
  React.useEffect(() => {
    const savedTheme = localStorage.getItem(storageKey) as Theme;
    if (savedTheme && (savedTheme === "light" || savedTheme === "dark" || (enableSystem && savedTheme === "system"))) {
      setThemeState(savedTheme);
    }
    setMounted(true);
  }, [storageKey, enableSystem]);

  // Listen for system theme changes
  React.useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    
    const updateSystemTheme = () => {
      setSystemTheme(mediaQuery.matches ? "dark" : "light");
    };

    // Set initial system theme
    updateSystemTheme();

    mediaQuery.addEventListener("change", updateSystemTheme);
    return () => mediaQuery.removeEventListener("change", updateSystemTheme);
  }, []);

  // Apply theme changes to DOM
  React.useEffect(() => {
    if (!mounted) return;

    const root = document.documentElement;
    
    // Disable transitions temporarily if requested
    if (disableTransitionOnChange) {
      const css = document.createElement("style");
      css.appendChild(
        document.createTextNode(
          `*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}`
        )
      );
      document.head.appendChild(css);

      // Re-enable transitions after a brief delay
      setTimeout(() => {
        document.head.removeChild(css);
      }, 1);
    }

    // Remove all theme classes first
    root.classList.remove("light", "dark");
    
    if (theme === "system") {
      // For system theme, don't add any class, let CSS media query handle it
      // The CSS is structured to handle this automatically
    } else {
      // Add the specific theme class
      root.classList.add(theme);
    }

    // Save to localStorage
    localStorage.setItem(storageKey, theme);
  }, [theme, systemTheme, mounted, storageKey, disableTransitionOnChange]);

  const setTheme = React.useCallback((newTheme: Theme) => {
    setThemeState(newTheme);
  }, []);

  const value = React.useMemo(
    () => ({
      theme,
      setTheme,
      resolvedTheme,
      systemTheme,
    }),
    [theme, setTheme, resolvedTheme, systemTheme]
  );

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = React.useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
}

// Theme toggle hook for easier usage
export function useThemeToggle() {
  const { theme, setTheme } = useTheme();

  const toggleTheme = React.useCallback(() => {
    const themes: Theme[] = ["light", "dark", "system"];
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  }, [theme, setTheme]);

  const setLightTheme = React.useCallback(() => setTheme("light"), [setTheme]);
  const setDarkTheme = React.useCallback(() => setTheme("dark"), [setTheme]);
  const setSystemTheme = React.useCallback(() => setTheme("system"), [setTheme]);

  return {
    theme,
    setTheme,
    toggleTheme,
    setLightTheme,
    setDarkTheme,
    setSystemTheme,
  };
}

// Theme utilities
export const getThemeIcon = (theme: Theme): string => {
  switch (theme) {
    case "light":
      return "☀️";
    case "dark":
      return "🌙";
    case "system":
      return "💻";
    default:
      return "💻";
  }
};

export const getThemeLabel = (theme: Theme): string => {
  switch (theme) {
    case "light":
      return "浅色模式";
    case "dark":
      return "深色模式";
    case "system":
      return "跟随系统";
    default:
      return "跟随系统";
  }
};

// Script to prevent flash of unstyled content (FOUC)
export const ThemeScript = () => {
  const script = `
    (function() {
      try {
        var theme = localStorage.getItem('${THEME_CONFIG.storageKey}') || '${THEME_CONFIG.defaultTheme}';
        var root = document.documentElement;
        
        if (theme === 'system') {
          // Let CSS media query handle system theme
          root.classList.remove('light', 'dark');
        } else {
          root.classList.remove('light', 'dark');
          root.classList.add(theme);
        }
      } catch (e) {}
    })();
  `;

  return <script dangerouslySetInnerHTML={{ __html: script }} />;
};
