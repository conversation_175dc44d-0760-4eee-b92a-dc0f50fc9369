---
title: "TypeScript 最佳实践"
description: "分享在实际项目中使用 TypeScript 的经验和最佳实践。"
date: "2024-12-20"
tags: ["TypeScript", "JavaScript", "前端开发", "最佳实践"]
category: "技术分享"
author: "Zer"
featured: false
readingTime: 10
---

# TypeScript 最佳实践

在现代前端开发中，TypeScript 已经成为了不可或缺的工具。本文将分享一些在实际项目中使用 TypeScript 的最佳实践。

## 类型定义

### 1. 使用接口而非类型别名

```typescript
// ✅ 推荐：使用接口
interface User {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
}

// ❌ 避免：对于对象类型使用类型别名
type User = {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
}
```

### 2. 合理使用泛型

```typescript
// ✅ 好的泛型使用
interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
}

interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

// 使用示例
const userResponse: ApiResponse<User> = await fetchUser(id);
const usersResponse: PaginatedResponse<User> = await fetchUsers();
```

### 3. 使用联合类型和字面量类型

```typescript
// 状态管理
type LoadingState = 'idle' | 'loading' | 'success' | 'error';

interface AsyncState<T> {
  data: T | null;
  status: LoadingState;
  error: string | null;
}

// 事件类型
type ButtonVariant = 'primary' | 'secondary' | 'danger' | 'ghost';
type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  onClick?: () => void;
}
```

## 函数类型

### 1. 函数重载

```typescript
// 函数重载示例
function formatDate(date: Date): string;
function formatDate(date: string): string;
function formatDate(date: number): string;
function formatDate(date: Date | string | number): string {
  if (date instanceof Date) {
    return date.toISOString();
  }
  if (typeof date === 'string') {
    return new Date(date).toISOString();
  }
  return new Date(date).toISOString();
}
```

### 2. 高阶函数类型

```typescript
// 高阶函数类型定义
type EventHandler<T = any> = (event: T) => void;
type AsyncEventHandler<T = any> = (event: T) => Promise<void>;

// 工具函数类型
type Predicate<T> = (item: T) => boolean;
type Mapper<T, U> = (item: T) => U;
type Reducer<T, U> = (acc: U, item: T) => U;

// 使用示例
function filter<T>(array: T[], predicate: Predicate<T>): T[] {
  return array.filter(predicate);
}

function map<T, U>(array: T[], mapper: Mapper<T, U>): U[] {
  return array.map(mapper);
}
```

## 实用工具类型

### 1. 内置工具类型

```typescript
interface User {
  id: string;
  name: string;
  email: string;
  password: string;
  createdAt: Date;
  updatedAt: Date;
}

// Partial - 所有属性可选
type UserUpdate = Partial<User>;

// Pick - 选择特定属性
type UserPublic = Pick<User, 'id' | 'name' | 'email'>;

// Omit - 排除特定属性
type UserCreate = Omit<User, 'id' | 'createdAt' | 'updatedAt'>;

// Required - 所有属性必需
type UserRequired = Required<User>;
```

### 2. 自定义工具类型

```typescript
// 深度可选
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// 深度只读
type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

// 非空类型
type NonNullable<T> = T extends null | undefined ? never : T;

// 函数参数类型
type Parameters<T extends (...args: any) => any> = T extends (...args: infer P) => any ? P : never;

// 函数返回类型
type ReturnType<T extends (...args: any) => any> = T extends (...args: any) => infer R ? R : any;
```

## React 中的 TypeScript

### 1. 组件 Props 类型

```typescript
import { ReactNode, HTMLAttributes } from 'react';

// 基础 Props
interface ButtonProps extends HTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  children: ReactNode;
}

// 泛型组件 Props
interface ListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => ReactNode;
  keyExtractor: (item: T) => string | number;
  emptyText?: string;
}

function List<T>({ items, renderItem, keyExtractor, emptyText }: ListProps<T>) {
  if (items.length === 0) {
    return <div>{emptyText || '暂无数据'}</div>;
  }

  return (
    <ul>
      {items.map((item, index) => (
        <li key={keyExtractor(item)}>
          {renderItem(item, index)}
        </li>
      ))}
    </ul>
  );
}
```

### 2. Hooks 类型

```typescript
import { useState, useEffect, useCallback, useMemo } from 'react';

// 自定义 Hook
function useAsyncState<T>(
  initialState: T
): [T, (value: T | Promise<T>) => Promise<void>] {
  const [state, setState] = useState<T>(initialState);

  const setAsyncState = useCallback(async (value: T | Promise<T>) => {
    const resolvedValue = await Promise.resolve(value);
    setState(resolvedValue);
  }, []);

  return [state, setAsyncState];
}

// API Hook
interface UseApiOptions {
  immediate?: boolean;
}

interface UseApiReturn<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  execute: () => Promise<void>;
}

function useApi<T>(
  apiCall: () => Promise<T>,
  options: UseApiOptions = {}
): UseApiReturn<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const execute = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  useEffect(() => {
    if (options.immediate) {
      execute();
    }
  }, [execute, options.immediate]);

  return { data, loading, error, execute };
}
```

## 错误处理

### 1. 类型安全的错误处理

```typescript
// 错误类型定义
abstract class AppError extends Error {
  abstract readonly code: string;
  abstract readonly statusCode: number;
}

class ValidationError extends AppError {
  readonly code = 'VALIDATION_ERROR';
  readonly statusCode = 400;
  
  constructor(message: string, public field: string) {
    super(message);
  }
}

class NotFoundError extends AppError {
  readonly code = 'NOT_FOUND';
  readonly statusCode = 404;
}

// Result 类型
type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };

// 使用示例
async function fetchUser(id: string): Promise<Result<User, AppError>> {
  try {
    const user = await api.getUser(id);
    return { success: true, data: user };
  } catch (error) {
    if (error instanceof AppError) {
      return { success: false, error };
    }
    return { success: false, error: new NotFoundError('用户不存在') };
  }
}
```

### 2. 类型守卫

```typescript
// 类型守卫函数
function isString(value: unknown): value is string {
  return typeof value === 'string';
}

function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value);
}

function isUser(value: unknown): value is User {
  return (
    typeof value === 'object' &&
    value !== null &&
    'id' in value &&
    'name' in value &&
    'email' in value
  );
}

// 使用类型守卫
function processUserData(data: unknown) {
  if (isUser(data)) {
    // 这里 data 的类型是 User
    console.log(data.name);
    console.log(data.email);
  } else {
    throw new ValidationError('无效的用户数据', 'user');
  }
}
```

## 配置优化

### 1. tsconfig.json 配置

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["DOM", "DOM.Iterable", "ES2020"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/utils/*": ["src/utils/*"],
      "@/types/*": ["src/types/*"]
    }
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "build",
    "dist"
  ]
}
```

### 2. 严格模式配置

```json
{
  "compilerOptions": {
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true
  }
}
```

## 总结

TypeScript 最佳实践的核心原则：

1. **类型安全优先**: 充分利用 TypeScript 的类型系统
2. **渐进式采用**: 从简单的类型注解开始，逐步深入
3. **工具类型活用**: 善用内置和自定义工具类型
4. **错误处理**: 建立完善的错误处理机制
5. **配置优化**: 合理配置编译选项

通过遵循这些最佳实践，我们可以写出更安全、更可维护的 TypeScript 代码。

---

*TypeScript 不仅仅是 JavaScript 的超集，更是提升代码质量的强大工具。掌握这些最佳实践，让你的代码更加健壮和可维护。*
