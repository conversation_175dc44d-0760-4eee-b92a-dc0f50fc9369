# Zer 个人博客网站

基于 Next.js 和 GSAP 构建的个人静态网站，用于分享文章、展示摄影作品和项目作品。

## 项目信息

### 技术栈
- **框架**: Next.js 15.3.1 (App Router)
- **语言**: TypeScript 5.x
- **样式**: Tailwind CSS 4.x
- **动画**: GSAP (待添加)
- **字体**: Geist Sans & Geist Mono
- **部署**: Vercel (推荐)

### 项目目录结构
```
zer-blog-nextjs/
├── src/
│   └── app/
│       ├── layout.tsx          # 根布局
│       ├── page.tsx            # 首页
│       ├── home/               # 主页
│       ├── globals.css         # 全局样式
│       └── favicon.ico
├── package.json                # 依赖配置
├── next.config.ts             # Next.js 配置
├── tsconfig.json              # TypeScript 配置
└── README.md                  # 项目说明
```

## 开发清单

### 🎯 核心功能
- [ ] **文章系统**
  - [ ] Markdown 文章解析和渲染
  - [ ] 文章列表页面
  - [ ] 文章详情页面
  - [ ] 文章分类和标签系统
  - [ ] 文章搜索功能
  - [ ] 阅读时间估算

- [ ] **图库系统**
  - [ ] 摄影作品展示页面
  - [ ] 图片懒加载和优化
  - [ ] 图片分类和筛选
  - [ ] 图片灯箱效果
  - [ ] 图片元数据显示（拍摄信息等）

- [ ] **项目展示**
  - [ ] 项目列表页面
  - [ ] 项目详情页面
  - [ ] 项目分类（参与项目/个人项目）
  - [ ] 技术栈标签
  - [ ] 项目链接和预览

### 🎨 用户体验
- [ ] **主题系统**
  - [ ] 深色/浅色主题切换
  - [ ] 主题偏好记忆
  - [ ] 平滑主题过渡动画

- [ ] **动画效果**
  - [ ] 集成 GSAP 动画库
  - [ ] 页面切换动画
  - [ ] 滚动触发动画
  - [ ] 加载动画
  - [ ] 悬停交互效果

- [ ] **响应式设计**
  - [ ] 移动端适配
  - [ ] 平板端适配
  - [ ] 桌面端优化

### 📄 页面结构
- [ ] **首页**
  - [ ] 个人简介区域
  - [ ] 最新文章展示
  - [ ] 精选项目展示
  - [ ] 联系方式

- [ ] **关于页面**
  - [ ] 个人详细介绍
  - [ ] 技能展示
  - [ ] 工作经历
  - [ ] 联系信息

- [ ] **导航系统**
  - [ ] 顶部导航栏
  - [ ] 移动端汉堡菜单
  - [ ] 面包屑导航
  - [ ] 返回顶部按钮

### 🔧 技术优化
- [ ] **SEO 优化**
  - [ ] Meta 标签优化
  - [ ] 结构化数据
  - [ ] 网站地图生成
  - [ ] robots.txt 配置

- [ ] **性能优化**
  - [ ] 图片优化和压缩
  - [ ] 代码分割
  - [ ] 缓存策略
  - [ ] 预加载关键资源

- [ ] **用户功能**
  - [ ] RSS 订阅
  - [ ] 评论系统（可选）
  - [ ] 访问统计
  - [ ] 搜索功能

### 🎯 额外功能
- [ ] **内容管理**
  - [ ] 文章草稿系统
  - [ ] 内容预览功能
  - [ ] 文章归档页面

- [ ] **社交功能**
  - [ ] 社交媒体链接
  - [ ] 文章分享功能
  - [ ] 友情链接页面

- [ ] **错误处理**
  - [ ] 自定义 404 页面
  - [ ] 错误边界处理
  - [ ] 加载状态提示

## 快速开始

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站。

## 部署

推荐使用 [Vercel](https://vercel.com) 进行部署，支持自动部署和预览。

## 详细开发步骤

### 第一阶段：项目准备 (1-2天)

#### 1.1 安装必要依赖
```bash
# 安装动画库
npm install gsap

# 安装Markdown处理相关
npm install gray-matter remark remark-html

# 安装类型定义
npm install --save-dev @types/node

# 安装图片处理（可选）
npm install sharp
```

#### 1.2 创建项目目录结构
```bash
# 创建内容目录
mkdir -p src/content/posts
mkdir -p src/content/projects
mkdir -p src/content/gallery

# 创建工具库目录
mkdir -p src/lib

# 创建页面目录
mkdir -p src/app/blog
mkdir -p src/app/gallery
mkdir -p src/app/projects
mkdir -p src/app/about

# 创建组件目录
mkdir -p src/app/components/ui
mkdir -p src/app/components/layout
```

#### 1.3 配置文件更新

**更新 `next.config.ts`：**
```typescript
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
  },
  experimental: {
    optimizePackageImports: ['gsap'],
  },
};

export default nextConfig;
```

**更新 `tsconfig.json` 路径别名：**
```json
{
  "compilerOptions": {
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/app/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/content/*": ["./src/content/*"]
    }
  }
}
```

### 第二阶段：基础架构 (2-3天)

#### 2.1 创建基础布局组件

**创建 `src/app/components/layout/Header.tsx`：**
```typescript
'use client';
import Link from 'next/link';
import { useState } from 'react';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="bg-white dark:bg-gray-900 shadow-sm">
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="text-xl font-bold">
              Zer's Blog
            </Link>
          </div>
          
          {/* 桌面端导航 */}
          <div className="hidden md:flex items-center space-x-8">
            <Link href="/blog" className="hover:text-blue-600">博客</Link>
            <Link href="/gallery" className="hover:text-blue-600">图库</Link>
            <Link href="/projects" className="hover:text-blue-600">项目</Link>
            <Link href="/about" className="hover:text-blue-600">关于</Link>
          </div>
          
          {/* 移动端菜单按钮 */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2"
            >
              ☰
            </button>
          </div>
        </div>
        
        {/* 移动端菜单 */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1">
              <Link href="/blog" className="block px-3 py-2">博客</Link>
              <Link href="/gallery" className="block px-3 py-2">图库</Link>
              <Link href="/projects" className="block px-3 py-2">项目</Link>
              <Link href="/about" className="block px-3 py-2">关于</Link>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
}
```

#### 2.2 创建主题切换组件

**创建 `src/app/components/ui/ThemeToggle.tsx`：**
```typescript
'use client';
import { useState, useEffect } from 'react';

export default function ThemeToggle() {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    const theme = localStorage.getItem('theme');
    if (theme === 'dark') {
      setIsDark(true);
      document.documentElement.classList.add('dark');
    }
  }, []);

  const toggleTheme = () => {
    if (isDark) {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    } else {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    }
    setIsDark(!isDark);
  };

  return (
    <button
      onClick={toggleTheme}
      className="p-2 rounded-lg bg-gray-200 dark:bg-gray-700"
    >
      {isDark ? '🌞' : '🌙'}
    </button>
  );
}
```

### 第三阶段：文章系统 (3-4天)

#### 3.1 创建Markdown处理工具

**创建 `src/lib/markdown.ts`：**
```typescript
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { remark } from 'remark';
import html from 'remark-html';

const postsDirectory = path.join(process.cwd(), 'src/content/posts');

export interface PostData {
  id: string;
  title: string;
  date: string;
  excerpt?: string;
  tags?: string[];
  content: string;
}

export function getAllPosts(): PostData[] {
  const fileNames = fs.readdirSync(postsDirectory);
  const allPostsData = fileNames.map((fileName) => {
    const id = fileName.replace(/\.md$/, '');
    const fullPath = path.join(postsDirectory, fileName);
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const matterResult = matter(fileContents);

    return {
      id,
      content: matterResult.content,
      ...matterResult.data,
    } as PostData;
  });

  return allPostsData.sort((a, b) => (a.date < b.date ? 1 : -1));
}

export async function getPostData(id: string): Promise<PostData> {
  const fullPath = path.join(postsDirectory, `${id}.md`);
  const fileContents = fs.readFileSync(fullPath, 'utf8');
  const matterResult = matter(fileContents);

  const processedContent = await remark()
    .use(html)
    .process(matterResult.content);
  const contentHtml = processedContent.toString();

  return {
    id,
    content: contentHtml,
    ...matterResult.data,
  } as PostData;
}
```

#### 3.2 创建博客页面

**创建 `src/app/blog/page.tsx`：**
```typescript
import Link from 'next/link';
import { getAllPosts } from '@/lib/markdown';

export default function BlogPage() {
  const posts = getAllPosts();

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">博客文章</h1>
      
      <div className="space-y-6">
        {posts.map((post) => (
          <article key={post.id} className="border-b pb-6">
            <h2 className="text-xl font-semibold mb-2">
              <Link href={`/blog/${post.id}`} className="hover:text-blue-600">
                {post.title}
              </Link>
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-2">
              {post.date}
            </p>
            {post.excerpt && (
              <p className="text-gray-700 dark:text-gray-300">
                {post.excerpt}
              </p>
            )}
            {post.tags && (
              <div className="mt-2 flex flex-wrap gap-2">
                {post.tags.map((tag) => (
                  <span
                    key={tag}
                    className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-sm"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </article>
        ))}
      </div>
    </div>
  );
}
```

### 第四阶段：图库系统 (2-3天)

#### 4.1 创建图库组件

**创建 `src/app/gallery/page.tsx`：**
```typescript
'use client';
import Image from 'next/image';
import { useState } from 'react';

interface Photo {
  id: string;
  src: string;
  alt: string;
  category: string;
  metadata?: {
    camera?: string;
    lens?: string;
    settings?: string;
  };
}

export default function GalleryPage() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [lightboxImage, setLightboxImage] = useState<Photo | null>(null);

  // 示例数据，实际应从文件系统或CMS获取
  const photos: Photo[] = [
    {
      id: '1',
      src: '/images/gallery/photo1.jpg',
      alt: '风景照片1',
      category: 'landscape',
      metadata: {
        camera: 'Canon EOS R5',
        lens: '24-70mm f/2.8',
        settings: 'f/8, 1/125s, ISO 100'
      }
    },
    // 更多照片...
  ];

  const categories = ['all', 'landscape', 'portrait', 'street', 'nature'];
  const filteredPhotos = selectedCategory === 'all' 
    ? photos 
    : photos.filter(photo => photo.category === selectedCategory);

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">摄影作品</h1>
      
      {/* 分类筛选 */}
      <div className="mb-8 flex flex-wrap gap-2">
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => setSelectedCategory(category)}
            className={`px-4 py-2 rounded-lg ${
              selectedCategory === category
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600'
            }`}
          >
            {category === 'all' ? '全部' : category}
          </button>
        ))}
      </div>
      
      {/* 图片网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredPhotos.map((photo) => (
          <div
            key={photo.id}
            className="cursor-pointer group"
            onClick={() => setLightboxImage(photo)}
          >
            <div className="relative aspect-square overflow-hidden rounded-lg">
              <Image
                src={photo.src}
                alt={photo.alt}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>
          </div>
        ))}
      </div>
      
      {/* 灯箱效果 */}
      {lightboxImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50"
          onClick={() => setLightboxImage(null)}
        >
          <div className="max-w-4xl max-h-full p-4">
            <Image
              src={lightboxImage.src}
              alt={lightboxImage.alt}
              width={800}
              height={600}
              className="max-w-full max-h-full object-contain"
            />
            {lightboxImage.metadata && (
              <div className="mt-4 text-white text-center">
                <p>{lightboxImage.metadata.camera}</p>
                <p>{lightboxImage.metadata.lens}</p>
                <p>{lightboxImage.metadata.settings}</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
```

### 第五阶段：项目展示 (2-3天)

#### 5.1 创建项目展示页面

**创建 `src/app/projects/page.tsx`：**
```typescript
import Link from 'next/link';
import Image from 'next/image';

interface Project {
  id: string;
  title: string;
  description: string;
  image: string;
  technologies: string[];
  category: 'personal' | 'work';
  links: {
    demo?: string;
    github?: string;
    website?: string;
  };
}

export default function ProjectsPage() {
  // 示例数据
  const projects: Project[] = [
    {
      id: 'blog-website',
      title: '个人博客网站',
      description: '基于Next.js构建的响应式个人博客，支持Markdown文章、图库展示等功能。',
      image: '/images/projects/blog.jpg',
      technologies: ['Next.js', 'TypeScript', 'Tailwind CSS', 'GSAP'],
      category: 'personal',
      links: {
        demo: 'https://example.com',
        github: 'https://github.com/username/blog'
      }
    },
    // 更多项目...
  ];

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">项目作品</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {projects.map((project) => (
          <div key={project.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <div className="relative h-48">
              <Image
                src={project.image}
                alt={project.title}
                fill
                className="object-cover"
              />
            </div>
            
            <div className="p-6">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-xl font-semibold">{project.title}</h2>
                <span className={`px-2 py-1 rounded text-sm ${
                  project.category === 'personal'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                }`}>
                  {project.category === 'personal' ? '个人项目' : '工作项目'}
                </span>
              </div>
              
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {project.description}
              </p>
              
              <div className="flex flex-wrap gap-2 mb-4">
                {project.technologies.map((tech) => (
                  <span
                    key={tech}
                    className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-sm"
                  >
                    {tech}
                  </span>
                ))}
              </div>
              
              <div className="flex gap-4">
                {project.links.demo && (
                  <a
                    href={project.links.demo}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    在线预览
                  </a>
                )}
                {project.links.github && (
                  <a
                    href={project.links.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    GitHub
                  </a>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
```

### 第六阶段：GSAP动画集成 (2-3天)

#### 6.1 创建动画工具

**创建 `src/lib/animations.ts`：**
```typescript
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export const fadeInUp = (element: string | Element, delay = 0) => {
  return gsap.fromTo(
    element,
    {
      opacity: 0,
      y: 50,
    },
    {
      opacity: 1,
      y: 0,
      duration: 0.8,
      delay,
      ease: 'power2.out',
    }
  );
};

export const staggerFadeIn = (elements: string | Element[], delay = 0.1) => {
  return gsap.fromTo(
    elements,
    {
      opacity: 0,
      y: 30,
    },
    {
      opacity: 1,
      y: 0,
      duration: 0.6,
      stagger: delay,
      ease: 'power2.out',
    }
  );
};

export const scrollTriggerAnimation = (
  element: string | Element,
  animation: gsap.core.Tween
) => {
  ScrollTrigger.create({
    trigger: element,
    start: 'top 80%',
    animation: animation,
  });
};
```

### 第七阶段：SEO和性能优化 (2-3天)

#### 7.1 创建SEO组件

**创建 `src/app/components/SEO.tsx`：**
```typescript
import Head from 'next/head';

interface SEOProps {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  url?: string;
}

export default function SEO({
  title,
  description,
  keywords,
  image = '/images/og-default.jpg',
  url
}: SEOProps) {
  const siteTitle = 'Zer\'s Blog';
  const fullTitle = title ? `${title} | ${siteTitle}` : siteTitle;

  return (
    <Head>
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      {keywords && <meta name="keywords" content={keywords} />}
      
      {/* Open Graph */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      {url && <meta property="og:url" content={url} />}
      <meta property="og:type" content="website" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      
      {/* 其他 */}
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <link rel="canonical" href={url} />
    </Head>
  );
}
```

### 第八阶段：完善和部署 (2-3天)

#### 8.1 创建示例内容

**创建示例博客文章 `src/content/posts/first-post.md`：**
```markdown
---
title: "欢迎来到我的博客"
date: "2024-01-01"
excerpt: "这是我的第一篇博客文章，介绍了这个网站的功能和特色。"
tags: ["介绍", "博客", "Next.js"]
---

# 欢迎来到我的博客

这是我使用 Next.js 构建的个人博客网站。在这里，我会分享：

- 技术文章和教程
- 摄影作品
- 个人项目展示
- 生活感悟

## 网站特色

- 响应式设计，支持各种设备
- 深色/浅色主题切换
- 流畅的动画效果
- SEO优化
- 快速加载

希望你喜欢这个网站！
```

#### 8.2 部署准备

**创建 `vercel.json`：**
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "framework": "nextjs",
  "regions": ["hkg1"]
}
```

## 开发建议

### 每日工作流程
1. **晨间规划** (15分钟)
   - 查看当日任务清单
   - 确定优先级
   - 设置开发环境

2. **专注开发** (2-4小时)
   - 关闭干扰源
   - 使用番茄工作法
   - 及时提交代码

3. **测试验证** (30分钟)
   - 功能测试
   - 响应式测试
   - 性能检查

4. **文档更新** (15分钟)
   - 更新README进度
   - 记录遇到的问题
   - 规划明日任务

### 推荐工具
- **编辑器**: VS Code + 相关插件
- **设计**: Figma (UI设计)
- **图片**: TinyPNG (压缩)
- **调试**: Chrome DevTools
- **版本控制**: Git + GitHub
- **部署**: Vercel CLI

### 学习资源
- [Next.js 官方文档](https://nextjs.org/docs)
- [GSAP 动画教程](https://greensock.com/learning/)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [TypeScript 手册](https://www.typescriptlang.org/docs/)

### 进度跟踪
建议每完成一个功能就在上面的清单中打勾 ✅，这样可以清楚看到开发进度。

---

*最后更新: 2024年*
