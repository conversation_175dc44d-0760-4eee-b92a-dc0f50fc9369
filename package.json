{"name": "zer-blog-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.3.4", "@types/mdx": "^2.0.13", "clsx": "^2.1.1", "critters": "^0.0.23", "gray-matter": "^4.0.3", "gsap": "^3.13.0", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "reading-time": "^1.5.0", "rehype-autolink-headings": "^7.1.0", "rehype-highlight": "^7.0.2", "rehype-slug": "^6.0.0", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "sharp": "^0.34.2", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.0", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}