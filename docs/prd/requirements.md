# Requirements

## Functional

- FR1: 系统应提供响应式导航菜单，支持桌面端横向导航和移动端汉堡菜单切换
- FR2: 个人介绍页面应展示完整简历信息，包括个人信息、技能标签云、项目经历时间线
- FR3: 摄影作品集应支持图片分类展示、Lightbox弹窗预览、懒加载优化
- FR4: 技术博客系统应支持Markdown文章渲染、代码语法高亮、标签分类筛选
- FR5: 所有页面应支持暗黑模式切换，并保持用户偏好设置
- FR6: 系统应提供文章搜索功能，支持标题和内容的关键词搜索
- FR7: 摄影作品应支持按类别（风景、人像、街拍等）进行分类浏览
- FR8: 个人项目展示应包含项目描述、技术栈、演示链接和源码链接
- FR9: 网站应提供RSS订阅功能，支持技术文章的订阅分发
- FR10: 系统应集成社交媒体分享功能，便于内容传播

## Non Functional

- NFR1: 首屏加载时间必须控制在2秒以内，后续页面切换响应时间不超过1秒
- NFR2: Lighthouse性能评分必须达到95分以上，包括性能、可访问性、最佳实践、SEO
- NFR3: 网站必须支持现代浏览器（Chrome 90+, Firefox 88+, Safari 14+, Edge 90+）
- NFR4: 移动端适配必须完美，支持Touch操作和滑动手势
- NFR5: 图片资源必须自动优化，支持WebP格式转换和多尺寸响应式加载
- NFR6: 代码分割和懒加载必须覆盖所有非关键路径，优化首屏性能
- NFR7: 必须符合WCAG 2.1 AA级可访问性标准，支持键盘导航和屏幕阅读器
- NFR8: 静态站点生成必须支持增量构建，避免全量重新构建导致的性能问题
