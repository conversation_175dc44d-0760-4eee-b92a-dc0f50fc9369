# Technical Assumptions

## Repository Structure: Monorepo

采用Monorepo结构管理整个项目，便于代码复用和依赖管理：
- `/src/app`: Next.js App Router页面和API路由
- `/src/components`: 可复用React组件库
- `/src/lib`: 工具函数和业务逻辑
- `/content`: Markdown文章和配置文件
- `/public`: 静态资源文件（图片、图标等）

## Service Architecture

**静态站点生成架构 (SSG)**:
- 采用Next.js 15 App Router实现完全静态化
- 构建时预生成所有页面，优化首屏加载性能
- 支持增量静态再生成(ISR)处理内容更新

**内容管理系统**:
- 基于文件系统的Markdown内容管理
- 使用gray-matter处理文章元数据
- Git版本控制管理内容变更历史

## Languages & Runtimes

- **前端语言**: TypeScript 5.x（类型安全，开发效率）
- **运行时**: Node.js 20.x LTS（稳定性和生态支持）
- **包管理器**: pnpm（速度快，节省磁盘空间）

## Frontend Frameworks

- **应用框架**: Next.js 15.3.1（App Router、SSG、性能优化）
- **UI库**: React 19（最新特性、生态丰富）
- **样式系统**: TailwindCSS 4.x（原子化CSS、快速开发）
- **动画库**: Framer Motion（流畅动画、用户体验）

## Styling & CSS

- **CSS框架**: TailwindCSS 4.x配置自定义设计系统
- **组件样式**: CSS Modules + Tailwind组合使用
- **响应式设计**: 移动优先的断点设计策略
- **暗黑模式**: 基于CSS变量的主题切换系统

## Content Management

- **文章格式**: Markdown + frontmatter元数据
- **图片处理**: Next.js Image组件自动优化
- **内容解析**: unified + remark + rehype生态
- **代码高亮**: Prism.js或highlight.js

## Deployment & Hosting

- **部署平台**: Vercel（免费额度、CDN加速、自动HTTPS）
- **CDN策略**: Vercel Edge Network全球加速
- **域名配置**: 自定义域名绑定和DNS配置
- **CI/CD**: GitHub Actions自动化构建和部署

## Database (If Needed)

虽然是静态站点，但考虑未来扩展可能需要的数据存储：
- **分析数据**: Vercel Analytics或Google Analytics
- **表单处理**: Vercel Forms或第三方服务
- **评论系统**: Giscus（基于GitHub Discussions）

## Third-party Integrations

- **分析工具**: Google Analytics 4 + Vercel Analytics
- **搜索服务**: Algolia DocSearch或本地搜索实现
- **社交分享**: 自定义分享按钮，支持微信、微博、Twitter
- **RSS服务**: 自动生成RSS/Atom订阅源
