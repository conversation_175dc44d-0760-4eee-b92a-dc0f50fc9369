# SEO and Metadata

## Dynamic Metadata Generation

```typescript
// lib/metadata.ts
export function generateMetadata(post: BlogPost) {
  return {
    title: `${post.title} | <PERSON><PERSON>'s Blog`,
    description: post.description,
    openGraph: {
      title: post.title,
      description: post.description,
      type: 'article',
      url: `https://zer.dev/blog/${post.slug}`,
      images: [
        {
          url: post.coverImage,
          width: 1200,
          height: 630,
          alt: post.title
        }
      ]
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.description,
      images: [post.coverImage]
    }
  }
}
```

## Structured Data

- **JSON-LD schema**：Person, Article, WebSite schema
- **Breadcrumb markup**：导航层次结构
- **Image metadata**：摄影作品的结构化数据
