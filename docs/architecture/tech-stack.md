# Tech Stack

## Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
| :------- | :--------- | :------ | :------ | :-------- |
| **Frontend Framework** | Next.js | 15.3.1 | 全栈React框架 | App Router、SSG、优秀的开发体验 |
| **UI Library** | React | 19.0.0 | 用户界面构建 | 最新特性、丰富生态 |
| **Styling Framework** | TailwindCSS | 4.x | 原子化CSS框架 | 快速开发、设计系统集成 |
| **Language** | TypeScript | 5.x | 类型安全开发 | 代码质量、开发效率 |
| **Content Processing** | MDX | Latest | Markdown + React组件 | 富文本内容、组件嵌入 |
| **Image Optimization** | Sharp | Latest | 图片处理和优化 | 自动WebP转换、多尺寸生成 |
| **Animation Library** | Framer Motion | Latest | 动画和交互 | 流畅动画、手势支持 |
| **Icon Library** | Lucide React | Latest | 图标系统 | 一致的线性图标风格 |
| **Build Tool** | Turbopack | Bundled | 快速构建工具 | Next.js 15内置，性能优秀 |
| **Package Manager** | pnpm | Latest | 依赖管理 | 速度快、节省磁盘空间 |
| **Deployment Platform** | Vercel | - | 静态站点托管 | 零配置部署、CDN加速 |
| **Code Quality** | ESLint + Prettier | Latest | 代码规范 | 一致的代码风格 |
