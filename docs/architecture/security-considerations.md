# Security Considerations

## Content Security Policy

```typescript
const cspHeader = `
  default-src 'self';
  script-src 'self' 'unsafe-eval' 'unsafe-inline';
  style-src 'self' 'unsafe-inline';
  img-src 'self' blob: data: https:;
  font-src 'self';
  connect-src 'self';
  frame-ancestors 'none';
`
```

## Privacy and Compliance

- **数据收集最小化**：仅收集必要的分析数据
- **Cookie政策**：明确的用户同意机制
- **第三方服务**：谨慎选择和配置
