# Component Architecture

## Component Organization

**设计原则**：
- **原子化设计**：Atoms → Molecules → Organisms → Templates → Pages
- **单一职责**：每个组件只负责一个特定功能
- **可复用性**：组件设计考虑多场景使用
- **一致性**：统一的API设计和命名规范

## Core UI Components

### Button Component
```typescript
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'ghost' | 'icon'
  size: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  children: React.ReactNode
  onClick?: () => void
}
```

### Card Component
```typescript
interface CardProps {
  variant: 'basic' | 'featured' | 'interactive'
  className?: string
  children: React.ReactNode
  href?: string // 可点击卡片
}
```

### Image Component
```typescript
interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  priority?: boolean
  sizes?: string
  className?: string
}
```
