# Zer个人博客静态网站 Fullstack Architecture Document

## Table of Contents

- [Zer个人博客静态网站 Fullstack Architecture Document](#table-of-contents)
  - [Introduction](./introduction.md)
    - [Change Log](./introduction.md#change-log)
  - [Architecture Overview](./architecture-overview.md)
    - [Core Architecture Principles](./architecture-overview.md#core-architecture-principles)
  - [Tech Stack](./tech-stack.md)
    - [Technology Stack Table](./tech-stack.md#technology-stack-table)
  - [Project Structure](./project-structure.md)
  - [Component Architecture](./component-architecture.md)
    - [Component Organization](./component-architecture.md#component-organization)
    - [Core UI Components](./component-architecture.md#core-ui-components)
      - [Button Component](./component-architecture.md#button-component)
      - [Card Component](./component-architecture.md#card-component)
      - [Image Component](./component-architecture.md#image-component)
  - [Content Management Architecture](./content-management-architecture.md)
    - [Markdown Processing Pipeline](./content-management-architecture.md#markdown-processing-pipeline)
    - [Content Types](./content-management-architecture.md#content-types)
      - [Blog Posts](./content-management-architecture.md#blog-posts)
      - [Photography Works](./content-management-architecture.md#photography-works)
  - [Styling Architecture](./styling-architecture.md)
    - [TailwindCSS 4 Configuration](./styling-architecture.md#tailwindcss-4-configuration)
    - [CSS Architecture](./styling-architecture.md#css-architecture)
  - [Performance Optimization](./performance-optimization.md)
    - [Static Generation Strategy](./performance-optimization.md#static-generation-strategy)
    - [Image Optimization](./performance-optimization.md#image-optimization)
    - [Code Splitting](./performance-optimization.md#code-splitting)
  - [SEO and Metadata](./seo-and-metadata.md)
    - [Dynamic Metadata Generation](./seo-and-metadata.md#dynamic-metadata-generation)
    - [Structured Data](./seo-and-metadata.md#structured-data)
  - [Deployment Architecture](./deployment-architecture.md)
    - [Build and Deployment Pipeline](./deployment-architecture.md#build-and-deployment-pipeline)
    - [Hosting Strategy](./deployment-architecture.md#hosting-strategy)
  - [Security Considerations](./security-considerations.md)
    - [Content Security Policy](./security-considerations.md#content-security-policy)
    - [Privacy and Compliance](./security-considerations.md#privacy-and-compliance)
  - [Development Workflow](./development-workflow.md)
    - [Local Development Setup](./development-workflow.md#local-development-setup)
    - [Content Creation Workflow](./development-workflow.md#content-creation-workflow)
  - [Monitoring and Analytics](./monitoring-and-analytics.md)
    - [Performance Monitoring](./monitoring-and-analytics.md#performance-monitoring)
    - [Content Analytics](./monitoring-and-analytics.md#content-analytics)
  - [Critical Development Rules](./critical-development-rules.md)
    - [TypeScript Standards](./critical-development-rules.md#typescript-standards)
    - [Component Standards](./critical-development-rules.md#component-standards)
    - [Performance Standards](./critical-development-rules.md#performance-standards)
    - [Accessibility Standards](./critical-development-rules.md#accessibility-standards)
  - [Next Steps and Implementation](./next-steps-and-implementation.md)
    - [Phase 1: Foundation Setup](./next-steps-and-implementation.md#phase-1-foundation-setup)
    - [Phase 2: Content System](./next-steps-and-implementation.md#phase-2-content-system)
    - [Phase 3: Performance Optimization](./next-steps-and-implementation.md#phase-3-performance-optimization)
    - [Phase 4: Deployment and Monitoring](./next-steps-and-implementation.md#phase-4-deployment-and-monitoring)
