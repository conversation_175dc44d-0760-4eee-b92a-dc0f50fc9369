# Critical Development Rules

## TypeScript Standards
- **严格模式**：启用所有TypeScript严格检查
- **类型定义**：为所有组件提供完整类型
- **接口优先**：使用interface而非type定义对象结构

## Component Standards
- **命名规范**：PascalCase组件名，camelCase函数名
- **文件组织**：一个组件一个文件，index.ts导出
- **Props接口**：所有props必须有类型定义

## Performance Standards
- **图片优化**：始终使用Next.js Image组件
- **懒加载**：非关键组件使用dynamic导入
- **Bundle分析**：定期分析bundle大小

## Accessibility Standards
- **语义化HTML**：正确使用heading层级
- **ARIA标签**：为交互元素提供完整标签
- **键盘导航**：所有功能支持键盘操作
