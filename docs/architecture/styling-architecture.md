# Styling Architecture

## TailwindCSS 4 Configuration

**Design Tokens集成**：
```typescript
// tailwind.config.ts
export default {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9ff',
          500: '#0ea5e9',
          900: '#0c4a6e'
        },
        neutral: {
          50: '#f8fafc',
          900: '#0f172a'
        }
      },
      fontFamily: {
        sans: ['Inter', 'Noto Sans SC', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace']
      },
      spacing: {
        '18': '4.5rem', // 72px
        '88': '22rem'    // 352px
      }
    }
  }
}
```

## CSS Architecture

- **组件样式**：Tailwind classes + CSS Modules for complex components
- **全局样式**：Typography, CSS custom properties for theming
- **暗黑模式**：CSS变量 + Tailwind's dark mode support
