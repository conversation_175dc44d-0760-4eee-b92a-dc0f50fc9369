# Performance Optimization

## Static Generation Strategy

```typescript
// next.config.ts
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true,
    formats: ['image/webp', 'image/avif']
  },
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react']
  }
}
```

## Image Optimization

1. **构建时优化**：Sharp自动处理、格式转换
2. **响应式图片**：多尺寸生成、srcset支持
3. **懒加载**：Intersection Observer API
4. **占位符**：模糊占位符、渐进式加载

## Code Splitting

- **Route-based splitting**：每个页面独立bundle
- **Component-based splitting**：大型组件动态导入
- **Third-party library optimization**：按需导入
