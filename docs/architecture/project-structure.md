# Project Structure

```
zer-blog-nextjs/
├── src/
│   ├── app/                    # Next.js 15 App Router
│   │   ├── (site)/            # 主站点路由组
│   │   │   ├── page.tsx       # 首页
│   │   │   ├── about/         # 关于页面
│   │   │   ├── portfolio/     # 作品集
│   │   │   ├── photography/   # 摄影作品
│   │   │   ├── blog/          # 博客文章
│   │   │   └── tags/          # 标签页面
│   │   ├── globals.css        # 全局样式
│   │   ├── layout.tsx         # 根布局
│   │   └── not-found.tsx      # 404页面
│   ├── components/            # React组件库
│   │   ├── ui/               # 基础UI组件
│   │   │   ├── Button.tsx
│   │   │   ├── Card.tsx
│   │   │   ├── Navigation.tsx
│   │   │   └── Image.tsx
│   │   ├── layout/           # 布局组件
│   │   ├── blog/             # 博客相关组件
│   │   └── photography/      # 摄影相关组件
│   ├── lib/                  # 工具函数和业务逻辑
│   │   ├── content.ts        # 内容处理
│   │   ├── metadata.ts       # SEO元数据
│   │   ├── utils.ts          # 通用工具函数
│   │   └── constants.ts      # 常量定义
│   └── types/                # TypeScript类型定义
├── content/                  # 内容管理
│   ├── blog/                # Markdown文章
│   ├── photography/         # 摄影作品元数据
│   └── config/              # 配置文件
├── public/                  # 静态资源
│   ├── images/             # 图片资源
│   ├── icons/              # 图标文件
│   └── documents/          # 文档文件（如简历PDF）
├── docs/                   # 项目文档
├── tailwind.config.ts      # TailwindCSS配置
├── next.config.ts          # Next.js配置
├── tsconfig.json          # TypeScript配置
└── package.json           # 项目依赖
```
