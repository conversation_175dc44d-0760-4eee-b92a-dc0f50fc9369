# Content Management Architecture

## Markdown Processing Pipeline

1. **内容创建**：Markdown文件 + Frontmatter元数据
2. **构建时处理**：unified + remark + rehype生态解析
3. **组件增强**：MDX支持React组件嵌入
4. **静态生成**：构建时预生成所有页面HTML

## Content Types

### Blog Posts
```typescript
interface BlogPost {
  slug: string
  title: string
  description: string
  publishedAt: string
  updatedAt?: string
  tags: string[]
  author: string
  featured?: boolean
  readingTime: number
  content: string
}
```

### Photography Works
```typescript
interface PhotographyWork {
  id: string
  title: string
  description?: string
  category: 'landscape' | 'portrait' | 'street'
  image: {
    src: string
    width: number
    height: number
    alt: string
  }
  exif?: {
    camera?: string
    lens?: string
    settings?: string
    location?: string
    date?: string
  }
  featured?: boolean
}
```
