{"name": "zer-blog-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build && next export"}, "dependencies": {"next": "^15.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "framer-motion": "^11.15.0", "gray-matter": "^4.0.3", "marked": "^14.1.3", "lucide-react": "^0.460.0", "@tailwindcss/typography": "^0.5.15"}, "devDependencies": {"@types/node": "^22.10.2", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "eslint": "^9.16.0", "eslint-config-next": "^15.1.0", "postcss": "^8.5.1", "tailwindcss": "^4.1.0", "typescript": "^5.7.2", "@types/marked": "^6.0.0"}}