# Zer 个人博客网站

基于 Next.js 和 GSAP 构建的个人静态网站，用于分享文章、展示摄影作品和项目作品。

## 项目信息

### 技术栈
- **框架**: Next.js 15.3.1 (App Router)
- **语言**: TypeScript 5.x
- **样式**: Tailwind CSS 4.x
- **动画**: GSAP (待添加)
- **字体**: Geist Sans & Geist Mono
- **部署**: Vercel (推荐)

### 项目目录结构
```
zer-blog-nextjs/
├── src/
│   └── app/
│       ├── layout.tsx          # 根布局
│       ├── page.tsx            # 首页
│       ├── home/               # 主页
│       ├── globals.css         # 全局样式
│       └── favicon.ico
├── package.json                # 依赖配置
├── next.config.ts             # Next.js 配置
├── tsconfig.json              # TypeScript 配置
└── README.md                  # 项目说明
```

## 开发清单

### 🎯 核心功能
- [ ] **文章系统**
  - [ ] Markdown 文章解析和渲染
  - [ ] 文章列表页面
  - [ ] 文章详情页面
  - [ ] 文章分类和标签系统
  - [ ] 文章搜索功能
  - [ ] 阅读时间估算

- [ ] **图库系统**
  - [ ] 摄影作品展示页面
  - [ ] 图片懒加载和优化
  - [ ] 图片分类和筛选
  - [ ] 图片灯箱效果
  - [ ] 图片元数据显示（拍摄信息等）

- [ ] **项目展示**
  - [ ] 项目列表页面
  - [ ] 项目详情页面
  - [ ] 项目分类（参与项目/个人项目）
  - [ ] 技术栈标签
  - [ ] 项目链接和预览

### 🎨 用户体验
- [ ] **主题系统**
  - [ ] 深色/浅色主题切换
  - [ ] 主题偏好记忆
  - [ ] 平滑主题过渡动画

- [ ] **动画效果**
  - [ ] 集成 GSAP 动画库
  - [ ] 页面切换动画
  - [ ] 滚动触发动画
  - [ ] 加载动画
  - [ ] 悬停交互效果

- [ ] **响应式设计**
  - [ ] 移动端适配
  - [ ] 平板端适配
  - [ ] 桌面端优化

### 📄 页面结构
- [ ] **首页**
  - [ ] 个人简介区域
  - [ ] 最新文章展示
  - [ ] 精选项目展示
  - [ ] 联系方式

- [ ] **关于页面**
  - [ ] 个人详细介绍
  - [ ] 技能展示
  - [ ] 工作经历
  - [ ] 联系信息

- [ ] **导航系统**
  - [ ] 顶部导航栏
  - [ ] 移动端汉堡菜单
  - [ ] 面包屑导航
  - [ ] 返回顶部按钮

### 🔧 技术优化
- [ ] **SEO 优化**
  - [ ] Meta 标签优化
  - [ ] 结构化数据
  - [ ] 网站地图生成
  - [ ] robots.txt 配置

- [ ] **性能优化**
  - [ ] 图片优化和压缩
  - [ ] 代码分割
  - [ ] 缓存策略
  - [ ] 预加载关键资源

- [ ] **用户功能**
  - [ ] RSS 订阅
  - [ ] 评论系统（可选）
  - [ ] 访问统计
  - [ ] 搜索功能

### 🎯 额外功能
- [ ] **内容管理**
  - [ ] 文章草稿系统
  - [ ] 内容预览功能
  - [ ] 文章归档页面

- [ ] **社交功能**
  - [ ] 社交媒体链接
  - [ ] 文章分享功能
  - [ ] 友情链接页面

- [ ] **错误处理**
  - [ ] 自定义 404 页面
  - [ ] 错误边界处理
  - [ ] 加载状态提示

## 快速开始

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站。

## 部署

推荐使用 [Vercel](https://vercel.com) 进行部署，支持自动部署和预览。

---

*最后更新: 2024年*
