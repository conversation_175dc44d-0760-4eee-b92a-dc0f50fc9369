# Zer个人博客静态网站 UI/UX Specification

## Introduction

这份文档定义了Zer个人博客静态网站的用户体验目标、信息架构、用户流程和视觉设计规范。它将作为视觉设计和前端开发的基础，确保统一的、以用户为中心的体验。

### Overall UX Goals & Principles

我们致力于创造一个**简洁、小清新、现代化**的个人博客体验，让访问者能够轻松地了解网站主人的专业背景、欣赏摄影作品、阅读技术文章。

### Target User Personas

**主要访问者**：
- **招聘方/潜在雇主**: HR、技术负责人，寻找合适的技术人才，需要快速了解专业背景和技能水平
- **同行开发者**: 技术学习者、开源贡献者，希望获取技术知识和学习资源
- **摄影爱好者**: 摄影师、设计师，欣赏高质量的摄影作品和视觉内容
- **内容读者**: 寻求有价值技术文章和学习资源的用户

**次要用户**：
- **内容管理者（网站主人）**: 技术开发者，需要简单高效的内容发布和管理流程

### Usability Goals

- **快速认知**: 访问者在5秒内理解网站主人的核心身份和专业方向
- **流畅浏览**: 用户能在3次点击内到达任何主要内容区域
- **沉浸阅读**: 技术文章页面提供无干扰的阅读体验，支持长时间专注阅读
- **高效导航**: 移动端用户能够单手完成所有主要操作
- **内容发现**: 用户能够通过多种方式（标签、分类、搜索）发现感兴趣的内容

### Design Principles

1. **简洁优于复杂** - 优先考虑清晰的信息传达而非视觉装饰
2. **内容为王** - 设计永远服务于内容，突出而非掩盖核心信息
3. **渐进式呈现** - 根据用户需求层次展示信息，避免信息过载
4. **一致性体验** - 在所有页面和设备上保持统一的交互模式
5. **可访问优先** - 从设计阶段就考虑所有用户群体的使用需求

### Change Log

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
| 2024-12-28 | 1.0 | 初始UI/UX规范创建 | Alex (UX Expert) |

## Information Architecture (IA)

### Site Map / Screen Inventory

```mermaid
graph TD
    A[首页 Homepage] --> B[关于 About]
    A --> C[作品集 Portfolio]
    A --> D[摄影 Photography]
    A --> E[博客 Blog]
    A --> F[标签 Tags]
    
    B --> B1[个人简历]
    B --> B2[技能详情]
    B --> B3[项目经历]
    B --> B4[联系方式]
    
    C --> C1[技术项目]
    C --> C2[开源贡献]
    C --> C3[项目详情页]
    
    D --> D1[风景摄影]
    D --> D2[人像摄影]
    D --> D3[街拍作品]
    D --> D4[图片详情/Lightbox]
    
    E --> E1[文章列表]
    E --> E2[文章详情页]
    E --> E3[分类浏览]
    E --> E4[搜索结果]
    
    F --> F1[技术标签]
    F --> F2[摄影标签]
    F --> F3[标签详情页]
```

### Navigation Structure

**主导航**: 固定顶部导航，包含核心功能模块入口
- 首页、关于、作品集、摄影、博客、标签

**次级导航**: 在相应页面内的二级分类导航
- 摄影作品的类别筛选
- 博客文章的分类和标签筛选
- 作品集的项目类型分类

**面包屑策略**: 仅在深层页面（文章详情、项目详情）使用，保持简洁明了

## User Flows

### 用户了解网站主人

**用户目标**: 快速了解网站主人的专业背景和个人特色

**入口点**: 首页Hero区域、关于页面链接、社交媒体引流

**成功标准**: 用户在1分钟内形成对网站主人的清晰认知

#### Flow Diagram

```mermaid
graph TD
    Start[访问首页] --> Hero[查看Hero区域]
    Hero --> Profile[个人头像和介绍]
    Profile --> Skills[核心技能展示]
    Skills --> Decision{是否需要更多信息?}
    Decision -->|是| About[点击关于页面]
    Decision -->|否| Explore[浏览其他内容]
    About --> Resume[查看完整简历]
    Resume --> Timeline[浏览工作经历时间线]
    Timeline --> Contact[查看联系方式]
    Contact --> Success[形成完整认知]
    Explore --> Success
```

**边缘情况和错误处理**:
- 加载缓慢: 提供骨架屏和渐进式加载
- 图片失效: 优雅降级到文字描述
- 移动端适配: 调整布局优先级，关键信息优先展示

### 欣赏摄影作品

**用户目标**: 浏览和欣赏高质量的摄影作品

**入口点**: 首页作品预览、摄影页面链接、社交媒体分享

**成功标准**: 用户浏览超过5张图片，停留时间超过2分钟

#### Flow Diagram

```mermaid
graph TD
    Start[进入摄影页面] --> Grid[查看作品网格]
    Grid --> Filter{需要筛选分类?}
    Filter -->|是| Category[选择作品类别]
    Filter -->|否| Browse[直接浏览]
    Category --> Browse
    Browse --> Click[点击感兴趣的图片]
    Click --> Lightbox[全屏Lightbox查看]
    Lightbox --> Navigate[键盘/手势导航]
    Navigate --> Info[查看图片信息]
    Info --> Next{查看下一张?}
    Next -->|是| Navigate
    Next -->|否| Close[关闭Lightbox]
    Close --> Continue[继续浏览或离开]
```

**边缘情况和错误处理**:
- 图片加载失败: 显示占位符和重试选项
- 移动端手势: 支持滑动切换和双击缩放
- 网络较慢: 提供不同质量的图片选项

### 阅读技术文章

**用户目标**: 获取有价值的技术知识和学习资源

**入口点**: 首页最新文章、博客页面、搜索结果、标签导航

**成功标准**: 用户完整阅读文章，参与互动（分享、收藏）

#### Flow Diagram

```mermaid
graph TD
    Start[进入博客页面] --> List[浏览文章列表]
    List --> Filter{需要筛选?}
    Filter -->|是| Search[搜索或标签筛选]
    Filter -->|否| Select[选择文章]
    Search --> Results[查看筛选结果]
    Results --> Select
    Select --> Article[进入文章详情页]
    Article --> TOC[查看文章目录]
    TOC --> Read[开始阅读]
    Read --> Interact[复制代码/分享]
    Interact --> Related[查看相关文章]
    Related --> Continue{继续阅读?}
    Continue -->|是| Select
    Continue -->|否| Exit[离开或返回列表]
```

**边缘情况和错误处理**:
- 长文章: 提供阅读进度指示和返回顶部功能
- 代码块: 支持语法高亮和一键复制
- 移动端阅读: 优化字体大小和行间距

## Wireframes & Mockups

**主要设计文件**: 将在Figma中创建高保真设计稿

### Key Screen Layouts

#### 首页 (Homepage)

**目的**: 创造强烈的第一印象，引导用户探索主要内容区域

**关键元素**:
- Hero区域：个人头像、姓名、职位、一句话介绍
- 核心技能展示：技术栈图标和标签云
- 功能模块入口：作品集、摄影、博客的视觉导航卡片
- 最新内容预览：最新文章和精选作品的缩略展示
- 社交媒体链接：GitHub、LinkedIn等平台的优雅链接

**交互说明**: 滚动时实现视差效果，卡片悬停有微动画反馈

#### 关于页面 (About)

**目的**: 提供详细的个人背景信息，建立专业信任感

**关键元素**:
- 个人简介：详细的职业背景和个人理念
- 技能展示：交互式技能雷达图或进度条
- 工作经历：时间线形式的工作和教育背景
- 个人兴趣：摄影、开源贡献等兴趣爱好
- 下载简历：PDF简历的下载链接

**交互说明**: 时间线支持展开/收起详情，技能图表有动画展示

#### 摄影作品集 (Photography)

**目的**: 优雅地展示摄影作品，提供沉浸式的视觉体验

**关键元素**:
- 分类筛选器：风景、人像、街拍等类别切换
- 响应式图片网格：瀑布流或规整网格布局
- 图片预览：鼠标悬停显示拍摄信息
- Lightbox查看器：全屏高清图片浏览
- 图片信息面板：拍摄参数、地点、时间等

**交互说明**: 支持键盘导航和移动端手势操作

#### 技术博客 (Blog)

**目的**: 提供优质的技术文章阅读体验

**关键元素**:
- 文章列表：卡片式布局，包含标题、摘要、标签、发布时间
- 搜索和筛选：关键词搜索和标签分类筛选
- 文章详情页：清晰的排版、代码高亮、目录导航
- 分享功能：社交媒体分享和链接复制
- 相关推荐：基于标签的相关文章推荐

**交互说明**: 文章页面支持阅读进度显示和目录锚点跳转

## Component Library / Design System

**设计系统方法**: 建立基于原子化设计理念的现代组件库，支持暗黑模式切换

### Core Components

#### Button 按钮组件

**目的**: 提供一致的交互入口和视觉反馈

**变体**: Primary（主要操作）、Secondary（次要操作）、Ghost（文本按钮）、Icon（图标按钮）

**状态**: Default、Hover、Active、Disabled、Loading

**使用指南**:
- Primary按钮每个视图区域最多使用一个
- Secondary按钮用于支持性操作
- Ghost按钮用于低优先级操作
- 确保足够的触控区域（至少44px）

#### Card 卡片组件

**目的**: 内容的结构化展示容器

**变体**: Basic（基础卡片）、Featured（特色卡片）、Interactive（可交互卡片）

**状态**: Default、Hover、Loading、Error

**使用指南**:
- 保持卡片内容的逻辑相关性
- 统一的圆角和阴影规范
- 支持骨架屏加载状态

#### Navigation 导航组件

**目的**: 提供清晰的站点导航体验

**变体**: Horizontal（桌面端横向）、Mobile（移动端汉堡菜单）、Breadcrumb（面包屑）

**状态**: Default、Active、Hover

**使用指南**:
- 当前页面始终高亮显示
- 移动端支持手势关闭
- 导航项目数量控制在7个以内

#### Image 图片组件

**目的**: 优化的图片展示和加载体验

**变体**: Responsive（响应式）、Avatar（头像）、Gallery（画廊）、Hero（大图）

**状态**: Loading、Loaded、Error

**使用指南**:
- 始终提供alt描述
- 支持懒加载和渐进式加载
- 提供占位符和错误状态

## Branding & Style Guide

### Visual Identity

设计风格追求**简洁、小清新、现代化**的视觉表达，营造专业而温馨的个人品牌形象。

### Color Palette

| Color Type    | Hex Code    | Usage                        |
| :------------ | :---------- | :--------------------------- |
| **Primary**   | #0EA5E9     | 主要操作按钮、链接、强调元素         |
| **Secondary** | #64748B     | 次要文本、图标、辅助信息           |
| **Accent**    | #10B981     | 成功状态、技能标签、积极反馈        |
| **Success**   | #10B981     | 成功操作、确认信息、正面反馈        |
| **Warning**   | #F59E0B     | 警告信息、重要提示、注意事项        |
| **Error**     | #EF4444     | 错误信息、失败状态、危险操作        |
| **Neutral**   | #F8FAFC     | 页面背景、卡片背景、分隔线         |

**暗黑模式色彩系统**:
| Color Type    | Dark Mode   | Usage                        |
| :------------ | :---------- | :--------------------------- |
| **Primary**   | #38BDF8     | 暗黑模式下的主色调             |
| **Background**| #0F172A     | 暗黑模式页面背景               |
| **Surface**   | #1E293B     | 暗黑模式卡片和表面             |
| **Text**      | #F1F5F9     | 暗黑模式主要文本               |

### Typography

**字体系统**:
- **中文主字体**: "Noto Sans SC", "PingFang SC", "Microsoft YaHei", sans-serif
- **英文主字体**: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif
- **代码字体**: "JetBrains Mono", "Fira Code", Consolas, monospace

**字体层级**:
| Element | Size | Weight | Line Height | Usage |
|:--------|:-----|:-------|:------------|:------|
| H1 | 2.5rem (40px) | 700 | 1.2 | 页面主标题 |
| H2 | 2rem (32px) | 600 | 1.3 | 章节标题 |
| H3 | 1.5rem (24px) | 600 | 1.4 | 子章节标题 |
| H4 | 1.25rem (20px) | 500 | 1.4 | 小标题 |
| Body Large | 1.125rem (18px) | 400 | 1.6 | 重要正文 |
| Body | 1rem (16px) | 400 | 1.6 | 常规正文 |
| Small | 0.875rem (14px) | 400 | 1.5 | 辅助信息 |
| Caption | 0.75rem (12px) | 400 | 1.4 | 说明文字 |

### Iconography

**图标库**: Lucide React（一致的线性图标风格）

**使用指南**:
- 图标尺寸：16px、20px、24px、32px
- 线条粗细：1.5px或2px
- 与文字对齐：确保垂直居中对齐
- 语义化使用：图标含义要清晰直观

### Spacing & Layout

**网格系统**: 12列响应式网格，基于CSS Grid和Flexbox

**间距系统**: 基于8px的倍数系统
- xs: 4px
- sm: 8px  
- md: 16px
- lg: 24px
- xl: 32px
- 2xl: 48px
- 3xl: 64px

**布局原则**:
- 大量留白营造呼吸感
- 内容最大宽度：1200px
- 侧边距：桌面端32px，移动端16px
- 组件间距：统一使用间距系统

## Accessibility Requirements

### Compliance Target

**标准**: WCAG 2.1 AA级可访问性合规

### Key Requirements

**视觉要求**:
- 颜色对比度：正文文字至少4.5:1，大文字至少3:1
- 焦点指示器：2px蓝色边框，圆角4px
- 文字缩放：支持到200%而不影响功能使用

**交互要求**:
- 键盘导航：所有交互元素可通过Tab键访问
- 触控目标：最小44px x 44px的触控区域
- 屏幕阅读器：提供完整的ARIA标签和语义化HTML

**内容要求**:
- 图片alt描述：所有装饰性和功能性图片提供描述
- 标题结构：遵循H1-H6的逻辑层级
- 表单标签：所有表单控件提供明确的标签

### Testing Strategy

- 使用axe-core进行自动化可访问性测试
- 定期使用屏幕阅读器（NVDA、VoiceOver）进行手动测试
- 键盘导航完整性测试
- 颜色对比度工具验证

## Responsiveness Strategy

### Breakpoints

| Breakpoint | Min Width | Max Width | Target Devices |
| :--------- | :-------- | :-------- | :------------- |
| Mobile     | 320px     | 767px     | 手机设备 |
| Tablet     | 768px     | 1023px    | 平板设备 |
| Desktop    | 1024px    | 1439px    | 桌面和笔记本 |
| Wide       | 1440px    | -         | 大屏显示器 |

### Adaptation Patterns

**布局变化**:
- 移动端：单列布局，垂直堆叠
- 平板端：2-3列网格布局
- 桌面端：多列复杂布局

**导航变化**:
- 移动端：汉堡菜单+底部导航
- 桌面端：顶部横向导航

**内容优先级**:
- 移动端：隐藏次要信息，突出核心内容
- 大屏：展示完整信息层次

**交互变化**:
- 移动端：手势操作（滑动、双击）
- 桌面端：鼠标悬停效果

## Animation & Micro-interactions

### Motion Principles

1. **目的性动画** - 每个动画都有明确的功能目标
2. **自然过渡** - 模拟现实世界的物理运动
3. **性能优先** - 优先使用transform和opacity属性
4. **尊重偏好** - 支持用户的动画减弱设置

### Key Animations

- **页面过渡**: 300ms ease-in-out淡入淡出效果
- **按钮交互**: 150ms ease-out的颜色和阴影变化
- **卡片悬停**: 200ms ease-out的轻微上浮和阴影增强
- **图片加载**: 渐进式淡入，避免跳跃式加载
- **菜单展开**: 250ms ease-out的滑动展开
- **滚动视差**: 轻微的background-attachment效果
- **加载状态**: 脉冲动画的骨架屏展示

## Performance Considerations

### Performance Goals

- **首屏加载**: < 2秒（3G网络环境）
- **交互响应**: < 100ms响应时间
- **动画帧率**: 保持60fps流畅动画
- **Lighthouse评分**: 所有指标≥90分

### Design Strategies

- **图片优化**: 使用WebP格式，提供多尺寸版本
- **懒加载**: 非关键图片和组件按需加载
- **代码分割**: 按页面和功能模块分割代码
- **缓存策略**: 利用浏览器缓存和CDN加速
- **Critical CSS**: 内联关键路径CSS

## Next Steps

### Immediate Actions

1. **创建高保真设计稿**: 在Figma中实现所有关键页面的视觉设计
2. **建立设计tokens**: 将色彩、字体、间距等设计变量标准化
3. **组件库原型**: 创建核心组件的交互原型

### Design Handoff Checklist

- [x] 所有用户流程已文档化
- [x] 组件库清单已完成
- [x] 可访问性要求已定义
- [x] 响应式策略已明确
- [x] 品牌指南已整合
- [x] 性能目标已建立
- [ ] 高保真设计稿已创建
- [ ] 交互原型已完成
- [ ] 设计tokens已导出

## Architecture Prompt

前端UI/UX规范已完成，请基于此文档创建详细的全栈架构设计。重点关注：

1. **Next.js 15 App Router的最佳实践配置**，支持静态站点生成
2. **TailwindCSS 4设计系统实现**，与本文档的色彩和字体系统保持一致
3. **组件库技术实现**，基于定义的设计系统创建可复用组件
4. **摄影作品和博客内容的技术架构**，支持高性能的图片处理和Markdown渲染
5. **响应式和可访问性的技术实现策略**，确保WCAG 2.1 AA合规 
