# Zer个人博客静态网站 Fullstack Architecture Document

## Introduction

本文档定义了Zer个人博客静态网站的完整技术架构，包括前端实现、内容管理和部署策略。该架构专为静态站点生成而设计，确保卓越的性能、SEO优化和开发体验。

### Change Log

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
| 2024-12-28 | 1.0 | 初始架构设计创建 | David (Solution Architect) |

## Architecture Overview

采用**Jamstack架构**模式，基于Next.js 15 App Router的静态站点生成，结合TailwindCSS 4的现代化设计系统，实现高性能的个人博客平台。

### Core Architecture Principles

1. **静态优先** - 预生成所有页面，最大化性能和SEO
2. **内容驱动** - 基于文件系统的内容管理，Git版本控制
3. **组件化设计** - 可复用的React组件库
4. **渐进增强** - 核心功能无需JavaScript，增强功能逐步添加
5. **性能优化** - 图片优化、代码分割、懒加载

## Tech Stack

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
| :------- | :--------- | :------ | :------ | :-------- |
| **Frontend Framework** | Next.js | 15.3.1 | 全栈React框架 | App Router、SSG、优秀的开发体验 |
| **UI Library** | React | 19.0.0 | 用户界面构建 | 最新特性、丰富生态 |
| **Styling Framework** | TailwindCSS | 4.x | 原子化CSS框架 | 快速开发、设计系统集成 |
| **Language** | TypeScript | 5.x | 类型安全开发 | 代码质量、开发效率 |
| **Content Processing** | MDX | Latest | Markdown + React组件 | 富文本内容、组件嵌入 |
| **Image Optimization** | Sharp | Latest | 图片处理和优化 | 自动WebP转换、多尺寸生成 |
| **Animation Library** | Framer Motion | Latest | 动画和交互 | 流畅动画、手势支持 |
| **Icon Library** | Lucide React | Latest | 图标系统 | 一致的线性图标风格 |
| **Build Tool** | Turbopack | Bundled | 快速构建工具 | Next.js 15内置，性能优秀 |
| **Package Manager** | pnpm | Latest | 依赖管理 | 速度快、节省磁盘空间 |
| **Deployment Platform** | Vercel | - | 静态站点托管 | 零配置部署、CDN加速 |
| **Code Quality** | ESLint + Prettier | Latest | 代码规范 | 一致的代码风格 |

## Project Structure

```
zer-blog-nextjs/
├── src/
│   ├── app/                    # Next.js 15 App Router
│   │   ├── (site)/            # 主站点路由组
│   │   │   ├── page.tsx       # 首页
│   │   │   ├── about/         # 关于页面
│   │   │   ├── portfolio/     # 作品集
│   │   │   ├── photography/   # 摄影作品
│   │   │   ├── blog/          # 博客文章
│   │   │   └── tags/          # 标签页面
│   │   ├── globals.css        # 全局样式
│   │   ├── layout.tsx         # 根布局
│   │   └── not-found.tsx      # 404页面
│   ├── components/            # React组件库
│   │   ├── ui/               # 基础UI组件
│   │   │   ├── Button.tsx
│   │   │   ├── Card.tsx
│   │   │   ├── Navigation.tsx
│   │   │   └── Image.tsx
│   │   ├── layout/           # 布局组件
│   │   ├── blog/             # 博客相关组件
│   │   └── photography/      # 摄影相关组件
│   ├── lib/                  # 工具函数和业务逻辑
│   │   ├── content.ts        # 内容处理
│   │   ├── metadata.ts       # SEO元数据
│   │   ├── utils.ts          # 通用工具函数
│   │   └── constants.ts      # 常量定义
│   └── types/                # TypeScript类型定义
├── content/                  # 内容管理
│   ├── blog/                # Markdown文章
│   ├── photography/         # 摄影作品元数据
│   └── config/              # 配置文件
├── public/                  # 静态资源
│   ├── images/             # 图片资源
│   ├── icons/              # 图标文件
│   └── documents/          # 文档文件（如简历PDF）
├── docs/                   # 项目文档
├── tailwind.config.ts      # TailwindCSS配置
├── next.config.ts          # Next.js配置
├── tsconfig.json          # TypeScript配置
└── package.json           # 项目依赖
```

## Component Architecture

### Component Organization

**设计原则**：
- **原子化设计**：Atoms → Molecules → Organisms → Templates → Pages
- **单一职责**：每个组件只负责一个特定功能
- **可复用性**：组件设计考虑多场景使用
- **一致性**：统一的API设计和命名规范

### Core UI Components

#### Button Component
```typescript
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'ghost' | 'icon'
  size: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  children: React.ReactNode
  onClick?: () => void
}
```

#### Card Component
```typescript
interface CardProps {
  variant: 'basic' | 'featured' | 'interactive'
  className?: string
  children: React.ReactNode
  href?: string // 可点击卡片
}
```

#### Image Component
```typescript
interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  priority?: boolean
  sizes?: string
  className?: string
}
```

## Content Management Architecture

### Markdown Processing Pipeline

1. **内容创建**：Markdown文件 + Frontmatter元数据
2. **构建时处理**：unified + remark + rehype生态解析
3. **组件增强**：MDX支持React组件嵌入
4. **静态生成**：构建时预生成所有页面HTML

### Content Types

#### Blog Posts
```typescript
interface BlogPost {
  slug: string
  title: string
  description: string
  publishedAt: string
  updatedAt?: string
  tags: string[]
  author: string
  featured?: boolean
  readingTime: number
  content: string
}
```

#### Photography Works
```typescript
interface PhotographyWork {
  id: string
  title: string
  description?: string
  category: 'landscape' | 'portrait' | 'street'
  image: {
    src: string
    width: number
    height: number
    alt: string
  }
  exif?: {
    camera?: string
    lens?: string
    settings?: string
    location?: string
    date?: string
  }
  featured?: boolean
}
```

## Styling Architecture

### TailwindCSS 4 Configuration

**Design Tokens集成**：
```typescript
// tailwind.config.ts
export default {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9ff',
          500: '#0ea5e9',
          900: '#0c4a6e'
        },
        neutral: {
          50: '#f8fafc',
          900: '#0f172a'
        }
      },
      fontFamily: {
        sans: ['Inter', 'Noto Sans SC', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace']
      },
      spacing: {
        '18': '4.5rem', // 72px
        '88': '22rem'    // 352px
      }
    }
  }
}
```

### CSS Architecture

- **组件样式**：Tailwind classes + CSS Modules for complex components
- **全局样式**：Typography, CSS custom properties for theming
- **暗黑模式**：CSS变量 + Tailwind's dark mode support

## Performance Optimization

### Static Generation Strategy

```typescript
// next.config.ts
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true,
    formats: ['image/webp', 'image/avif']
  },
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react']
  }
}
```

### Image Optimization

1. **构建时优化**：Sharp自动处理、格式转换
2. **响应式图片**：多尺寸生成、srcset支持
3. **懒加载**：Intersection Observer API
4. **占位符**：模糊占位符、渐进式加载

### Code Splitting

- **Route-based splitting**：每个页面独立bundle
- **Component-based splitting**：大型组件动态导入
- **Third-party library optimization**：按需导入

## SEO and Metadata

### Dynamic Metadata Generation

```typescript
// lib/metadata.ts
export function generateMetadata(post: BlogPost) {
  return {
    title: `${post.title} | Zer's Blog`,
    description: post.description,
    openGraph: {
      title: post.title,
      description: post.description,
      type: 'article',
      url: `https://zer.dev/blog/${post.slug}`,
      images: [
        {
          url: post.coverImage,
          width: 1200,
          height: 630,
          alt: post.title
        }
      ]
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.description,
      images: [post.coverImage]
    }
  }
}
```

### Structured Data

- **JSON-LD schema**：Person, Article, WebSite schema
- **Breadcrumb markup**：导航层次结构
- **Image metadata**：摄影作品的结构化数据

## Deployment Architecture

### Build and Deployment Pipeline

```yaml
# GitHub Actions workflow
name: Deploy Blog
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
      - run: pnpm install
      - run: pnpm build
      - run: pnpm export
      - name: Deploy to Vercel
        uses: vercel/action@v1
```

### Hosting Strategy

- **主站托管**：Vercel静态站点部署
- **CDN分发**：Vercel Edge Network全球加速
- **域名配置**：自定义域名 + 自动HTTPS
- **缓存策略**：静态资源长期缓存、HTML短期缓存

## Security Considerations

### Content Security Policy

```typescript
const cspHeader = `
  default-src 'self';
  script-src 'self' 'unsafe-eval' 'unsafe-inline';
  style-src 'self' 'unsafe-inline';
  img-src 'self' blob: data: https:;
  font-src 'self';
  connect-src 'self';
  frame-ancestors 'none';
`
```

### Privacy and Compliance

- **数据收集最小化**：仅收集必要的分析数据
- **Cookie政策**：明确的用户同意机制
- **第三方服务**：谨慎选择和配置

## Development Workflow

### Local Development Setup

```bash
# 项目初始化
git clone <repository>
cd zer-blog-nextjs
pnpm install

# 开发服务器
pnpm dev

# 构建和预览
pnpm build
pnpm start

# 代码质量检查
pnpm lint
pnpm type-check
```

### Content Creation Workflow

1. **创建Markdown文件**：在`content/blog/`目录下
2. **添加Frontmatter**：标题、描述、标签等元数据
3. **本地预览**：开发服务器实时预览
4. **提交部署**：Git commit触发自动部署

## Monitoring and Analytics

### Performance Monitoring

- **Core Web Vitals**：Lighthouse CI集成
- **Runtime Performance**：Vercel Analytics
- **Error Tracking**：Next.js内置错误边界

### Content Analytics

- **访问统计**：隐私友好的分析方案
- **搜索优化**：Google Search Console集成
- **性能指标**：页面加载时间、跳出率

## Critical Development Rules

### TypeScript Standards
- **严格模式**：启用所有TypeScript严格检查
- **类型定义**：为所有组件提供完整类型
- **接口优先**：使用interface而非type定义对象结构

### Component Standards
- **命名规范**：PascalCase组件名，camelCase函数名
- **文件组织**：一个组件一个文件，index.ts导出
- **Props接口**：所有props必须有类型定义

### Performance Standards
- **图片优化**：始终使用Next.js Image组件
- **懒加载**：非关键组件使用dynamic导入
- **Bundle分析**：定期分析bundle大小

### Accessibility Standards
- **语义化HTML**：正确使用heading层级
- **ARIA标签**：为交互元素提供完整标签
- **键盘导航**：所有功能支持键盘操作

## Next Steps and Implementation

### Phase 1: Foundation Setup
1. **项目初始化**：Next.js 15 + TailwindCSS 4配置
2. **基础组件**：Button、Card、Navigation组件开发
3. **设计系统**：Color tokens、Typography配置

### Phase 2: Content System
1. **Markdown处理**：MDX配置和内容解析
2. **博客功能**：文章列表、详情页、标签系统
3. **摄影作品集**：图片展示、分类、Lightbox

### Phase 3: Performance Optimization
1. **图片优化**：Sharp配置、懒加载实现
2. **SEO优化**：Metadata生成、结构化数据
3. **性能测试**：Lighthouse评分优化

### Phase 4: Deployment and Monitoring
1. **部署配置**：Vercel部署、域名配置
2. **监控设置**：性能监控、错误追踪
3. **内容发布**：内容迁移、测试验证

---

**架构设计完成** ✅

这份架构文档为您的个人博客项目提供了完整的技术实现指南。基于Next.js 15和TailwindCSS 4的现代化技术栈，确保了卓越的性能、开发体验和用户体验。

**下一步建议**: 开始文档验证阶段，确保所有规划文档的一致性和完整性。 
