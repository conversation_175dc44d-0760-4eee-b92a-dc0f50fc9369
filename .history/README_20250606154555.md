# Zer 个人博客网站

基于 Next.js 和 GSAP 构建的个人静态网站，用于分享文章、展示摄影作品和项目作品。

## 项目信息

### 技术栈
- **框架**: Next.js 15.3.1 (App Router)
- **语言**: TypeScript 5.x
- **样式**: Tailwind CSS 4.x
- **动画**: GSAP (待添加)
- **字体**: Geist Sans & Geist Mono
- **部署**: Vercel (推荐)

### 项目目录结构
```
zer-blog-nextjs/
├── src/
│   └── app/
│       ├── layout.tsx          # 根布局
│       ├── page.tsx            # 首页
│       ├── home/               # 主页
│       ├── globals.css         # 全局样式
│       └── favicon.ico
├── package.json                # 依赖配置
├── next.config.ts             # Next.js 配置
├── tsconfig.json              # TypeScript 配置
└── README.md                  # 项目说明
```

## 开发清单

### 🎯 核心功能
- [ ] **文章系统**
  - [ ] Markdown 文章解析和渲染
  - [ ] 文章列表页面
  - [ ] 文章详情页面
  - [ ] 文章分类和标签系统
  - [ ] 文章搜索功能
  - [ ] 阅读时间估算

- [ ] **图库系统**
  - [ ] 摄影作品展示页面
  - [ ] 图片懒加载和优化
  - [ ] 图片分类和筛选
  - [ ] 图片灯箱效果
  - [ ] 图片元数据显示（拍摄信息等）

- [ ] **项目展示**
  - [ ] 项目列表页面
  - [ ] 项目详情页面
  - [ ] 项目分类（参与项目/个人项目）
  - [ ] 技术栈标签
  - [ ] 项目链接和预览

### 🎨 用户体验
- [ ] **主题系统**
  - [ ] 深色/浅色主题切换
  - [ ] 主题偏好记忆
  - [ ] 平滑主题过渡动画

- [ ] **动画效果**
  - [ ] 集成 GSAP 动画库
  - [ ] 页面切换动画
  - [ ] 滚动触发动画
  - [ ] 加载动画
  - [ ] 悬停交互效果

- [ ] **响应式设计**
  - [ ] 移动端适配
  - [ ] 平板端适配
  - [ ] 桌面端优化

### 📄 页面结构
- [ ] **首页**
  - [ ] 个人简介区域
  - [ ] 最新文章展示
  - [ ] 精选项目展示
  - [ ] 联系方式

- [ ] **关于页面**
  - [ ] 个人详细介绍
  - [ ] 技能展示
  - [ ] 工作经历
  - [ ] 联系信息

- [ ] **导航系统**
  - [ ] 顶部导航栏
  - [ ] 移动端汉堡菜单
  - [ ] 面包屑导航
  - [ ] 返回顶部按钮

### 🔧 技术优化
- [ ] **SEO 优化**
  - [ ] Meta 标签优化
  - [ ] 结构化数据
  - [ ] 网站地图生成
  - [ ] robots.txt 配置

- [ ] **性能优化**
  - [ ] 图片优化和压缩
  - [ ] 代码分割
  - [ ] 缓存策略
  - [ ] 预加载关键资源

- [ ] **用户功能**
  - [ ] RSS 订阅
  - [ ] 评论系统（可选）
  - [ ] 访问统计
  - [ ] 搜索功能

### 🎯 额外功能
- [ ] **内容管理**
  - [ ] 文章草稿系统
  - [ ] 内容预览功能
  - [ ] 文章归档页面

- [ ] **社交功能**
  - [ ] 社交媒体链接
  - [ ] 文章分享功能
  - [ ] 友情链接页面

- [ ] **错误处理**
  - [ ] 自定义 404 页面
  - [ ] 错误边界处理
  - [ ] 加载状态提示

## 快速开始

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站。

## 开发步骤指南

### 🚀 第一阶段：项目准备 (1-2天)

**目标**: 搭建基础开发环境和项目结构

1. **安装核心依赖**
   ```bash
   npm install gsap
   npm install gray-matter remark remark-html
   npm install @types/node
   ```

2. **项目结构规划**
   ```
   src/
   ├── app/
   │   ├── blog/           # 文章相关页面
   │   ├── gallery/        # 图库页面
   │   ├── projects/       # 项目展示页面
   │   ├── about/          # 关于页面
   │   └── components/     # 公共组件
   ├── content/
   │   ├── posts/          # Markdown文章
   │   ├── projects/       # 项目数据
   │   └── images/         # 图片资源
   └── lib/
       ├── markdown.ts     # Markdown处理
       ├── utils.ts        # 工具函数
       └── animations.ts   # GSAP动画
   ```

3. **基础配置**
   - [ ] 配置 `next.config.ts` 支持图片优化
   - [ ] 设置 TypeScript 路径别名
   - [ ] 配置 Tailwind CSS 自定义主题

### 🏗️ 第二阶段：基础架构 (3-5天)

**目标**: 建立网站的基本框架和导航系统

1. **布局系统**
   - [ ] 创建响应式头部导航组件
   - [ ] 设计页脚组件
   - [ ] 实现移动端汉堡菜单
   - [ ] 添加深色/浅色主题切换

2. **页面结构**
   - [ ] 重新设计首页布局
   - [ ] 创建关于页面
   - [ ] 设置404错误页面
   - [ ] 实现页面间的基础路由

3. **基础动画**
   - [ ] 集成GSAP库
   - [ ] 实现页面切换动画
   - [ ] 添加基础的悬停效果

### 📝 第三阶段：文章系统 (5-7天)

**目标**: 完成博客文章的完整功能

1. **Markdown处理**
   - [ ] 设置Markdown文件解析
   - [ ] 实现代码高亮
   - [ ] 添加文章元数据支持
   - [ ] 计算阅读时间

2. **文章页面**
   - [ ] 文章列表页面 (`/blog`)
   - [ ] 文章详情页面 (`/blog/[slug]`)
   - [ ] 文章分类和标签系统
   - [ ] 文章搜索功能

3. **内容管理**
   - [ ] 创建示例文章
   - [ ] 实现文章排序和分页
   - [ ] 添加相关文章推荐

### 🖼️ 第四阶段：图库系统 (4-6天)

**目标**: 实现摄影作品展示功能

1. **图片处理**
   - [ ] 配置Next.js图片优化
   - [ ] 实现图片懒加载
   - [ ] 添加图片压缩和格式转换

2. **图库界面**
   - [ ] 网格布局图库页面
   - [ ] 图片灯箱效果
   - [ ] 图片分类和筛选
   - [ ] 图片元数据显示

3. **交互体验**
   - [ ] 图片缩放和平移
   - [ ] 键盘导航支持
   - [ ] 图片预加载优化

### 🚀 第五阶段：项目展示 (3-4天)

**目标**: 完成项目作品展示功能

1. **项目数据结构**
   - [ ] 设计项目数据模型
   - [ ] 创建项目配置文件
   - [ ] 实现项目分类系统

2. **展示页面**
   - [ ] 项目列表页面 (`/projects`)
   - [ ] 项目详情页面 (`/projects/[id]`)
   - [ ] 技术栈标签展示
   - [ ] 项目链接和预览

### 🎨 第六阶段：用户体验优化 (4-6天)

**目标**: 提升网站的视觉效果和交互体验

1. **高级动画**
   - [ ] 滚动触发动画
   - [ ] 页面加载动画
   - [ ] 微交互动画
   - [ ] 视差滚动效果

2. **主题系统**
   - [ ] 完善深色主题
   - [ ] 主题切换动画
   - [ ] 主题偏好记忆

3. **响应式优化**
   - [ ] 移动端体验优化
   - [ ] 平板端适配
   - [ ] 触摸手势支持

### 🔧 第七阶段：性能和SEO (3-5天)

**目标**: 优化网站性能和搜索引擎友好性

1. **SEO优化**
   - [ ] 配置Meta标签
   - [ ] 实现结构化数据
   - [ ] 生成网站地图
   - [ ] 配置robots.txt

2. **性能优化**
   - [ ] 代码分割优化
   - [ ] 图片优化策略
   - [ ] 缓存策略配置
   - [ ] 预加载关键资源

3. **监控和分析**
   - [ ] 集成Google Analytics
   - [ ] 性能监控设置
   - [ ] 错误追踪配置

### 🎯 第八阶段：完善和部署 (2-3天)

**目标**: 完成最终的功能完善和部署

1. **最终功能**
   - [ ] RSS订阅功能
   - [ ] 评论系统（可选）
   - [ ] 社交分享功能
   - [ ] 友情链接页面

2. **测试和优化**
   - [ ] 跨浏览器测试
   - [ ] 移动端测试
   - [ ] 性能测试
   - [ ] 无障碍访问测试

3. **部署上线**
   - [ ] Vercel部署配置
   - [ ] 域名配置
   - [ ] SSL证书设置
   - [ ] 监控和备份

## 开发建议

### 📋 每日工作流程
1. **选择当前阶段的任务**
2. **在README中勾选完成的项目**
3. **提交代码并写清楚的commit信息**
4. **测试功能是否正常工作**
5. **记录遇到的问题和解决方案**

### 🛠️ 开发工具推荐
- **代码编辑器**: VS Code + 相关插件
- **设计工具**: Figma (设计稿)
- **图片处理**: TinyPNG (压缩)
- **调试工具**: Chrome DevTools
- **版本控制**: Git + GitHub

### 📚 学习资源
- [Next.js 官方文档](https://nextjs.org/docs)
- [GSAP 动画教程](https://greensock.com/learning)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [TypeScript 手册](https://www.typescriptlang.org/docs)

## 部署

推荐使用 [Vercel](https://vercel.com) 进行部署，支持自动部署和预览。

---

*最后更新: 2024年*
