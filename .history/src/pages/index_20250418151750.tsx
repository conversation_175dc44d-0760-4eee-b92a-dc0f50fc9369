import Head from 'next/head'
import Link from 'next/link'
import { useState } from 'react'

function HomePage() {
  const [count, setCount] = useState(0)
  
  return (
    <div className="container mx-auto px-4 py-8">
      <Head>
        <title>我的博客首页</title>
        <meta name="description" content="欢迎来到我的个人博客" />
      </Head>
      
      <main>
        <h1 className="text-3xl font-bold mb-6">欢迎来到我的博客</h1>
        
        <p className="text-lg mb-4">这是使用Next.js Pages Router创建的首页</p>
        
        <div className="my-6 p-4 border rounded-lg bg-gray-50">
          <p>计数器演示: {count}</p>
          <button 
            onClick={() => setCount(count + 1)}
            className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            增加
          </button>
        </div>
        
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">导航链接</h2>
          <ul className="space-y-2">
            <li>
              <Link href="/about" className="text-blue-500 hover:underline">
                关于我
              </Link>
            </li>
            <li>
              <Link href="/blog" className="text-blue-500 hover:underline">
                博客文章
              </Link>
            </li>
          </ul>
        </div>
      </main>
    </div>
  )
}

export default HomePage;
