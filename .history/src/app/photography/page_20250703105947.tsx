"use client";

import { Metadata } from "next";
import * as React from "react";
import { Card, CardContent } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { PHOTOGRAPHY_CONFIG } from "@/lib/constants";
import {
  getAllPhotos,
  getFeaturedPhotos,
  getPhotosByCategory,
  getPhotoCategories,
  getPhotoStats
} from "@/lib/photography";
import { PhotoGrid } from "@/components/photography/PhotoGrid";
import { Lightbox, useLightbox } from "@/components/photography/Lightbox";
import { CategoryFilter, useCategoryFilter } from "@/components/photography/CategoryFilter";
import { Photo, PhotoCategory } from "@/types";

// 由于组件现在是客户端组件，metadata 需要移到 layout 或使用 generateMetadata
// export const metadata: Metadata = {
//   title: "摄影作品",
//   description: "欣赏我的摄影作品，记录生活中的美好瞬间",
// };

export default function PhotographyPage() {
  const [photos, setPhotos] = React.useState<Photo[]>([]);
  const [categories, setCategories] = React.useState<PhotoCategory[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [photoCounts, setPhotoCounts] = React.useState<Record<string, number>>({});

  const { activeCategory, handleCategoryChange } = useCategoryFilter();
  const { isOpen, currentIndex, openLightbox, closeLightbox, goToNext, goToPrevious } = useLightbox(photos);

  // 加载数据
  React.useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [allPhotos, photoCategories, stats] = await Promise.all([
          getAllPhotos(),
          getPhotoCategories(),
          getPhotoStats(),
        ]);

        setPhotos(allPhotos);
        setCategories(photoCategories);

        // 设置照片计数
        const counts: Record<string, number> = {
          total: stats.totalPhotos,
        };
        stats.categories.forEach(cat => {
          counts[cat.id] = cat.count;
        });
        setPhotoCounts(counts);
      } catch (error) {
        console.error('Failed to load photography data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);
  // 根据分类过滤照片
  const filteredPhotos = React.useMemo(() => {
    if (!activeCategory) return photos;
    return photos.filter(photo => photo.category === activeCategory);
  }, [photos, activeCategory]);

  // 获取精选照片
  const featuredPhotos = React.useMemo(() => {
    return photos.filter(photo => photo.featured);
  }, [photos]);

  return (
    <div className="min-h-screen bg-background">
      {/* 页面头部 */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              📷 摄影作品集
            </h1>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              用镜头记录世界的美好，捕捉生活中的每一个精彩瞬间
            </p>

            {/* 统计信息 */}
            <div className="flex justify-center gap-8 text-center">
              <div>
                <div className="text-2xl font-bold">{photoCounts.total || 0}</div>
                <div className="text-sm text-blue-200">总照片数</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{categories.length}</div>
                <div className="text-sm text-blue-200">分类数</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{featuredPhotos.length}</div>
                <div className="text-sm text-blue-200">精选作品</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* 精选作品区域 */}
        {featuredPhotos.length > 0 && (
          <section className="mb-16">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-foreground mb-4">⭐ 精选作品</h2>
              <p className="text-muted-foreground">
                这些是我最满意的摄影作品，展现了不同的拍摄技巧和创意视角
              </p>
            </div>

            <PhotoGrid
              photos={featuredPhotos}
              onPhotoClick={openLightbox}
              columns={3}
              showInfo={true}
              className="mb-8"
            />
          </section>
        )}

        {/* 分类过滤器 */}
        <section className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <h2 className="text-2xl font-bold text-foreground">
              {activeCategory
                ? categories.find(cat => cat.id === activeCategory)?.name || '所有作品'
                : '所有作品'
              }
            </h2>

            <CategoryFilter
              categories={categories}
              activeCategory={activeCategory}
              onCategoryChange={handleCategoryChange}
              photoCounts={photoCounts}
            />
          </div>
        </section>

        {/* 主要照片网格 */}
        <section>
          <PhotoGrid
            photos={filteredPhotos}
            onPhotoClick={openLightbox}
            columns={4}
            showInfo={false}
            loading={loading}
          />
        </section>

        {/* 器材展示区域 */}
        <section className="mt-16 bg-muted/30 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-foreground mb-4">📷 摄影器材</h2>
            <p className="text-muted-foreground">
              工欲善其事，必先利其器。这些是我常用的摄影设备
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-4xl mb-4">📸</div>
              <h3 className="font-semibold text-foreground mb-3">相机机身</h3>
              <ul className="text-muted-foreground space-y-2">
                <li>Canon EOS R5</li>
                <li>Sony A7R IV</li>
                <li>Fujifilm X-T4</li>
                <li>Nikon D850</li>
              </ul>
            </div>

            <div className="text-center">
              <div className="text-4xl mb-4">🔍</div>
              <h3 className="font-semibold text-foreground mb-3">镜头</h3>
              <ul className="text-muted-foreground space-y-2">
                <li>RF 24-70mm f/2.8L IS USM</li>
                <li>FE 85mm f/1.4 GM</li>
                <li>XF 16-55mm f/2.8 R LM WR</li>
                <li>AF-S VR Micro-NIKKOR 105mm</li>
              </ul>
            </div>

            <div className="text-center">
              <div className="text-4xl mb-4">⚙️</div>
              <h3 className="font-semibold text-foreground mb-3">配件</h3>
              <ul className="text-muted-foreground space-y-2">
                <li>碳纤维三脚架</li>
                <li>ND滤镜组</li>
                <li>外置闪光灯</li>
                <li>无线快门遥控器</li>
              </ul>
            </div>
          </div>
        </section>
      </div>

      {/* Lightbox 组件 */}
      <Lightbox
        photos={filteredPhotos}
        currentIndex={currentIndex}
        isOpen={isOpen}
        onClose={closeLightbox}
        onNext={goToNext}
        onPrevious={goToPrevious}
        showThumbnails={true}
        showCaption={true}
        showCounter={true}
      />
    </div>
  );
}
