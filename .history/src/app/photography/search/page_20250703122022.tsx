import * as React from "react";
import { Suspense } from "react";

"use client";

import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { searchPhotos } from "@/lib/photography";
import { PhotoGrid } from "@/components/photography/PhotoGrid";
import { Lightbox, useLightbox } from "@/components/photography/Lightbox";
import { Button } from "@/components/ui/Button";
import { Photo } from "@/types";

function PhotoSearchContent() {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  
  const [photos, setPhotos] = React.useState<Photo[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState(query);

  const { isOpen, currentIndex, openLightbox, closeLightbox, goToNext, goToPrevious } = useLightbox(photos);

  // 搜索照片
  const handleSearch = React.useCallback(async (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setPhotos([]);
      return;
    }

    try {
      setLoading(true);
      const results = await searchPhotos(searchTerm);
      setPhotos(results);
    } catch (error) {
      console.error('Failed to search photos:', error);
      setPhotos([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始搜索
  React.useEffect(() => {
    if (query) {
      handleSearch(query);
    }
  }, [query, handleSearch]);

  // 处理搜索表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // 更新 URL
      const url = new URL(window.location.href);
      url.searchParams.set('q', searchQuery.trim());
      window.history.pushState({}, '', url.toString());
      
      handleSearch(searchQuery.trim());
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* 返回按钮 */}
      <div className="container mx-auto px-4 py-6">
        <Link href="/photography">
          <Button variant="outline" size="sm">
            ← 返回作品集
          </Button>
        </Link>
      </div>

      {/* 搜索头部 */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <div className="text-6xl mb-4">🔍</div>
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              搜索照片
            </h1>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              搜索您感兴趣的摄影作品，支持标题、描述、标签和地点搜索
            </p>
            
            {/* 搜索表单 */}
            <form onSubmit={handleSubmit} className="max-w-2xl mx-auto">
              <div className="flex gap-4">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="输入搜索关键词..."
                  className="flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white/50"
                />
                <Button 
                  type="submit" 
                  variant="secondary"
                  className="px-8 py-3 bg-white text-blue-600 hover:bg-gray-100"
                  disabled={loading}
                >
                  {loading ? '搜索中...' : '搜索'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* 搜索结果 */}
      <div className="container mx-auto px-4 py-12">
        {query && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-foreground mb-2">
              搜索结果
            </h2>
            <p className="text-muted-foreground">
              关键词 "<span className="font-semibold text-foreground">{query}</span>" 
              的搜索结果，共找到 {photos.length} 张照片
            </p>
          </div>
        )}

        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="aspect-square bg-muted rounded-lg animate-pulse"></div>
            ))}
          </div>
        ) : photos.length > 0 ? (
          <PhotoGrid
            photos={photos}
            onPhotoClick={openLightbox}
            columns={4}
            showInfo={true}
          />
        ) : query ? (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-2xl font-bold text-foreground mb-4">
              未找到相关照片
            </h3>
            <p className="text-muted-foreground mb-8">
              没有找到与 "<span className="font-semibold">{query}</span>" 相关的照片，
              请尝试其他关键词
            </p>
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                搜索建议：
              </div>
              <div className="flex flex-wrap justify-center gap-2">
                {['风景', '人像', '街拍', '建筑', '自然', '旅行'].map((suggestion) => (
                  <button
                    key={suggestion}
                    onClick={() => {
                      setSearchQuery(suggestion);
                      handleSearch(suggestion);
                    }}
                    className="px-3 py-1 bg-muted text-muted-foreground text-sm rounded-md hover:bg-muted/80 transition-colors"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">📷</div>
            <h3 className="text-2xl font-bold text-foreground mb-4">
              开始搜索照片
            </h3>
            <p className="text-muted-foreground mb-8">
              在上方输入关键词来搜索您感兴趣的摄影作品
            </p>
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                热门搜索：
              </div>
              <div className="flex flex-wrap justify-center gap-2">
                {['日落', '山景', '城市', '人像', '微距', '黑白'].map((popular) => (
                  <button
                    key={popular}
                    onClick={() => {
                      setSearchQuery(popular);
                      handleSearch(popular);
                    }}
                    className="px-3 py-1 bg-muted text-muted-foreground text-sm rounded-md hover:bg-muted/80 transition-colors"
                  >
                    {popular}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Lightbox 组件 */}
      <Lightbox
        photos={photos}
        currentIndex={currentIndex}
        isOpen={isOpen}
        onClose={closeLightbox}
        onNext={goToNext}
        onPrevious={goToPrevious}
        showThumbnails={true}
        showCaption={true}
        showCounter={true}
      />
    </div>
  );
}

export default function PhotoSearchPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-48 mb-4"></div>
          <div className="h-4 bg-muted rounded w-96 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="aspect-square bg-muted rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    }>
      <PhotoSearchContent />
    </Suspense>
  );
}
