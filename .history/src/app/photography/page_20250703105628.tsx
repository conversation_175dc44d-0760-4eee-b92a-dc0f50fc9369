"use client";

import { Metadata } from "next";
import * as React from "react";
import { Card, CardContent } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { PHOTOGRAPHY_CONFIG } from "@/lib/constants";
import {
  getAllPhotos,
  getFeaturedPhotos,
  getPhotosByCategory,
  getPhotoCategories,
  getPhotoStats
} from "@/lib/photography";
import { PhotoGrid } from "@/components/photography/PhotoGrid";
import { Lightbox, useLightbox } from "@/components/photography/Lightbox";
import { CategoryFilter, useCategoryFilter } from "@/components/photography/CategoryFilter";
import { Photo, PhotoCategory } from "@/types";

// 由于组件现在是客户端组件，metadata 需要移到 layout 或使用 generateMetadata
// export const metadata: Metadata = {
//   title: "摄影作品",
//   description: "欣赏我的摄影作品，记录生活中的美好瞬间",
// };

// 模拟摄影作品数据
const mockPhotos = [
  {
    id: "sunset-landscape-1",
    title: "夕阳西下",
    description: "在山顶拍摄的日落美景",
    src: "/images/photography/sunset-1.jpg",
    alt: "夕阳西下的山景",
    category: "landscape",
    tags: ["日落", "山景", "自然"],
    date: "2024-12-20",
    camera: "Canon EOS R5",
    lens: "RF 24-70mm f/2.8L IS USM",
    settings: {
      aperture: "f/8",
      shutter: "1/125s",
      iso: "100",
      focalLength: "35mm",
    },
    location: "黄山",
    featured: true,
  },
  {
    id: "street-portrait-1",
    title: "街头人像",
    description: "捕捉城市中的人文瞬间",
    src: "/images/photography/street-1.jpg",
    alt: "街头人像摄影",
    category: "street",
    tags: ["人像", "街拍", "城市"],
    date: "2024-12-15",
    camera: "Sony A7R IV",
    lens: "FE 85mm f/1.4 GM",
    settings: {
      aperture: "f/2.8",
      shutter: "1/250s",
      iso: "400",
      focalLength: "85mm",
    },
    location: "上海",
    featured: true,
  },
  {
    id: "architecture-1",
    title: "现代建筑",
    description: "城市建筑的几何美学",
    src: "/images/photography/architecture-1.jpg",
    alt: "现代建筑摄影",
    category: "architecture",
    tags: ["建筑", "几何", "现代"],
    date: "2024-12-10",
    camera: "Fujifilm X-T4",
    lens: "XF 16-55mm f/2.8 R LM WR",
    settings: {
      aperture: "f/5.6",
      shutter: "1/200s",
      iso: "200",
      focalLength: "24mm",
    },
    location: "深圳",
    featured: false,
  },
  {
    id: "nature-macro-1",
    title: "花卉微距",
    description: "春天花朵的细节之美",
    src: "/images/photography/macro-1.jpg",
    alt: "花卉微距摄影",
    category: "nature",
    tags: ["微距", "花卉", "春天"],
    date: "2024-12-05",
    camera: "Nikon D850",
    lens: "AF-S VR Micro-NIKKOR 105mm f/2.8G IF-ED",
    settings: {
      aperture: "f/5.6",
      shutter: "1/320s",
      iso: "320",
      focalLength: "105mm",
    },
    location: "杭州西湖",
    featured: false,
  },
  {
    id: "travel-landscape-1",
    title: "海边日出",
    description: "海边的第一缕阳光",
    src: "/images/photography/travel-1.jpg",
    alt: "海边日出摄影",
    category: "travel",
    tags: ["日出", "海景", "旅行"],
    date: "2024-11-28",
    camera: "Canon EOS R6",
    lens: "RF 15-35mm f/2.8L IS USM",
    settings: {
      aperture: "f/11",
      shutter: "1/60s",
      iso: "100",
      focalLength: "20mm",
    },
    location: "青岛",
    featured: false,
  },
  {
    id: "portrait-studio-1",
    title: "工作室人像",
    description: "专业灯光下的人像作品",
    src: "/images/photography/portrait-1.jpg",
    alt: "工作室人像摄影",
    category: "portrait",
    tags: ["人像", "工作室", "专业"],
    date: "2024-11-20",
    camera: "Sony A7 III",
    lens: "FE 70-200mm f/2.8 GM OSS",
    settings: {
      aperture: "f/2.8",
      shutter: "1/160s",
      iso: "200",
      focalLength: "135mm",
    },
    location: "工作室",
    featured: false,
  },
];

export default function PhotographyPage() {
  const featuredPhotos = mockPhotos.filter(photo => photo.featured);
  const allPhotos = mockPhotos;
  const categories = PHOTOGRAPHY_CONFIG.categories;

  const getCategoryName = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category ? category.name : categoryId;
  };

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Header */}
      <section className="text-center py-12">
        <h1 className="text-4xl font-bold text-foreground mb-4">摄影作品</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          用镜头记录生活中的美好瞬间，分享我眼中的世界
        </p>
      </section>

      {/* Categories */}
      <section className="py-8">
        <div className="flex flex-wrap justify-center gap-4 mb-8">
          <Button variant="outline" size="sm">
            全部
          </Button>
          {categories.map((category) => (
            <Button key={category.id} variant="ghost" size="sm">
              {category.name}
            </Button>
          ))}
        </div>
      </section>

      {/* Featured Photos */}
      {featuredPhotos.length > 0 && (
        <section className="py-8">
          <h2 className="text-2xl font-bold text-foreground mb-6">精选作品</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {featuredPhotos.map((photo) => (
              <Card key={photo.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="aspect-[4/3] bg-muted flex items-center justify-center">
                  <span className="text-6xl">📷</span>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">
                      {new Date(photo.date).toLocaleDateString('zh-CN')}
                    </span>
                    <span className="px-2 py-1 bg-secondary text-secondary-foreground text-xs rounded-md">
                      {getCategoryName(photo.category)}
                    </span>
                  </div>
                  <h3 className="text-xl font-semibold text-foreground mb-2">
                    {photo.title}
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    {photo.description}
                  </p>
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <div className="flex justify-between">
                      <span>相机:</span>
                      <span>{photo.camera}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>镜头:</span>
                      <span>{photo.lens}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>参数:</span>
                      <span>
                        {photo.settings.aperture} • {photo.settings.shutter} • ISO {photo.settings.iso}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>地点:</span>
                      <span>{photo.location}</span>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-4">
                    {photo.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      )}

      {/* Photo Gallery */}
      <section className="py-8">
        <h2 className="text-2xl font-bold text-foreground mb-6">作品集</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {allPhotos.map((photo) => (
            <Card key={photo.id} className="overflow-hidden hover:shadow-md transition-shadow group cursor-pointer">
              <div className="aspect-square bg-muted flex items-center justify-center relative overflow-hidden">
                <span className="text-4xl">📸</span>
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity text-white text-center">
                    <h4 className="font-semibold mb-1">{photo.title}</h4>
                    <p className="text-sm">{getCategoryName(photo.category)}</p>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </section>

      {/* Equipment Section */}
      <section className="py-12 bg-muted/30 rounded-2xl mt-12">
        <div className="max-w-4xl mx-auto text-center px-8">
          <h2 className="text-2xl font-bold text-foreground mb-6">摄影器材</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="font-semibold text-foreground mb-3">相机</h3>
              <ul className="text-muted-foreground space-y-1">
                <li>Canon EOS R5</li>
                <li>Sony A7R IV</li>
                <li>Fujifilm X-T4</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-foreground mb-3">镜头</h3>
              <ul className="text-muted-foreground space-y-1">
                <li>RF 24-70mm f/2.8L</li>
                <li>FE 85mm f/1.4 GM</li>
                <li>XF 16-55mm f/2.8</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-foreground mb-3">配件</h3>
              <ul className="text-muted-foreground space-y-1">
                <li>碳纤维三脚架</li>
                <li>ND滤镜组</li>
                <li>外置闪光灯</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
