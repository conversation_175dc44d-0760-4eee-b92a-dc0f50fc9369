import { Metadata } from "next";
import Link from "next/link";
import { notFound } from "next/navigation";
import { getPhotosByCategory, getPhotoCategories } from "@/lib/photography";
import { PhotoCategoryClient } from "@/components/photography/PhotoCategoryClient";
import { Button } from "@/components/ui/Button";
import { PhotoCategory } from "@/types";
import { PHOTOGRAPHY_CONFIG } from "@/lib/constants";

interface CategoryPageProps {
  params: Promise<{ category: string }>;
}

// 生成页面元数据
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const { category } = await params;
  const categoryInfo = PHOTOGRAPHY_CONFIG.categories.find(cat => cat.id === category);

  if (!categoryInfo) {
    return {
      title: "分类未找到",
      description: "请求的分类不存在",
    };
  }

  return {
    title: `${categoryInfo.name} - 摄影作品`,
    description: categoryInfo.description,
  };
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { category: categoryId } = await params;

  // 验证分类是否存在
  const categories = await getPhotoCategories();
  const category = categories.find(cat => cat.id === categoryId);

  if (!category) {
    notFound();
  }

  // 获取该分类的照片
  const photos = await getPhotosByCategory(categoryId);

  const getCategoryIcon = (categoryId: string): string => {
    const icons: Record<string, string> = {
      landscape: "🏔️",
      portrait: "👤",
      street: "🏙️",
      architecture: "🏢",
      nature: "🌿",
      travel: "✈️",
    };
    return icons[categoryId] || "📷";
  };

  return (
    <div className="min-h-screen bg-background">
      {/* 返回按钮 */}
      <div className="container mx-auto px-4 py-6">
        <Link href="/photography">
          <Button variant="outline" size="sm">
            ← 返回作品集
          </Button>
        </Link>
      </div>

      {/* 分类头部 */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <div className="text-6xl mb-4">
              {getCategoryIcon(category.id)}
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {category.name}
            </h1>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              {category.description}
            </p>
            
            {/* 统计信息 */}
            <div className="flex justify-center gap-8 text-center">
              <div>
                <div className="text-2xl font-bold">{photos.length}</div>
                <div className="text-sm text-blue-200">作品数量</div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {photos.filter(photo => photo.featured).length}
                </div>
                <div className="text-sm text-blue-200">精选作品</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 照片网格 */}
      <div className="container mx-auto px-4 py-12">
        {photos.length > 0 ? (
          <PhotoGrid
            photos={photos}
            onPhotoClick={openLightbox}
            columns={4}
            showInfo={true}
            loading={loading}
          />
        ) : (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">📷</div>
            <h3 className="text-2xl font-bold text-foreground mb-4">
              暂无{category.name}作品
            </h3>
            <p className="text-muted-foreground mb-8">
              这个分类下还没有上传作品，敬请期待！
            </p>
            <Link href="/photography">
              <Button>
                浏览其他作品
              </Button>
            </Link>
          </div>
        )}
      </div>

      {/* Lightbox 组件 */}
      <Lightbox
        photos={photos}
        currentIndex={currentIndex}
        isOpen={isOpen}
        onClose={closeLightbox}
        onNext={goToNext}
        onPrevious={goToPrevious}
        showThumbnails={true}
        showCaption={true}
        showCounter={true}
      />
    </div>
  );
}

// 生成静态路径
export async function generateStaticParams() {
  return PHOTOGRAPHY_CONFIG.categories.map((category) => ({
    category: category.id,
  }));
}
