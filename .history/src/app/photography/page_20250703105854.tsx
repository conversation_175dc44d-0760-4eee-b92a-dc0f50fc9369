"use client";

import { Metadata } from "next";
import * as React from "react";
import { Card, CardContent } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { PHOTOGRAPHY_CONFIG } from "@/lib/constants";
import {
  getAllPhotos,
  getFeaturedPhotos,
  getPhotosByCategory,
  getPhotoCategories,
  getPhotoStats
} from "@/lib/photography";
import { PhotoGrid } from "@/components/photography/PhotoGrid";
import { Lightbox, useLightbox } from "@/components/photography/Lightbox";
import { CategoryFilter, useCategoryFilter } from "@/components/photography/CategoryFilter";
import { Photo, PhotoCategory } from "@/types";

// 由于组件现在是客户端组件，metadata 需要移到 layout 或使用 generateMetadata
// export const metadata: Metadata = {
//   title: "摄影作品",
//   description: "欣赏我的摄影作品，记录生活中的美好瞬间",
// };

export default function PhotographyPage() {
  const [photos, setPhotos] = React.useState<Photo[]>([]);
  const [categories, setCategories] = React.useState<PhotoCategory[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [photoCounts, setPhotoCounts] = React.useState<Record<string, number>>({});

  const { activeCategory, handleCategoryChange } = useCategoryFilter();
  const { isOpen, currentIndex, openLightbox, closeLightbox, goToNext, goToPrevious } = useLightbox(photos);

  // 加载数据
  React.useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [allPhotos, photoCategories, stats] = await Promise.all([
          getAllPhotos(),
          getPhotoCategories(),
          getPhotoStats(),
        ]);

        setPhotos(allPhotos);
        setCategories(photoCategories);

        // 设置照片计数
        const counts: Record<string, number> = {
          total: stats.totalPhotos,
        };
        stats.categories.forEach(cat => {
          counts[cat.id] = cat.count;
        });
        setPhotoCounts(counts);
      } catch (error) {
        console.error('Failed to load photography data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);
  // 根据分类过滤照片
  const filteredPhotos = React.useMemo(() => {
    if (!activeCategory) return photos;
    return photos.filter(photo => photo.category === activeCategory);
  }, [photos, activeCategory]);

  // 获取精选照片
  const featuredPhotos = React.useMemo(() => {
    return photos.filter(photo => photo.featured);
  }, [photos]);

  return (
    <div className="min-h-screen bg-background">
      {/* 页面头部 */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              📷 摄影作品集
            </h1>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              用镜头记录世界的美好，捕捉生活中的每一个精彩瞬间
            </p>

            {/* 统计信息 */}
            <div className="flex justify-center gap-8 text-center">
              <div>
                <div className="text-2xl font-bold">{photoCounts.total || 0}</div>
                <div className="text-sm text-blue-200">总照片数</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{categories.length}</div>
                <div className="text-sm text-blue-200">分类数</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{featuredPhotos.length}</div>
                <div className="text-sm text-blue-200">精选作品</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* 精选作品区域 */}
        {featuredPhotos.length > 0 && (
          <section className="mb-16">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-foreground mb-4">⭐ 精选作品</h2>
              <p className="text-muted-foreground">
                这些是我最满意的摄影作品，展现了不同的拍摄技巧和创意视角
              </p>
            </div>

            <PhotoGrid
              photos={featuredPhotos}
              onPhotoClick={openLightbox}
              columns={3}
              showInfo={true}
              className="mb-8"
            />
          </section>
        )}

        {/* 分类过滤器 */}
        <section className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <h2 className="text-2xl font-bold text-foreground">
              {activeCategory
                ? categories.find(cat => cat.id === activeCategory)?.name || '所有作品'
                : '所有作品'
              }
            </h2>

            <CategoryFilter
              categories={categories}
              activeCategory={activeCategory}
              onCategoryChange={handleCategoryChange}
              photoCounts={photoCounts}
            />
          </div>
        </section>

export default function PhotographyPage() {
  const featuredPhotos = mockPhotos.filter(photo => photo.featured);
  const allPhotos = mockPhotos;
  const categories = PHOTOGRAPHY_CONFIG.categories;

  const getCategoryName = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category ? category.name : categoryId;
  };

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Header */}
      <section className="text-center py-12">
        <h1 className="text-4xl font-bold text-foreground mb-4">摄影作品</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          用镜头记录生活中的美好瞬间，分享我眼中的世界
        </p>
      </section>

      {/* Categories */}
      <section className="py-8">
        <div className="flex flex-wrap justify-center gap-4 mb-8">
          <Button variant="outline" size="sm">
            全部
          </Button>
          {categories.map((category) => (
            <Button key={category.id} variant="ghost" size="sm">
              {category.name}
            </Button>
          ))}
        </div>
      </section>

      {/* Featured Photos */}
      {featuredPhotos.length > 0 && (
        <section className="py-8">
          <h2 className="text-2xl font-bold text-foreground mb-6">精选作品</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {featuredPhotos.map((photo) => (
              <Card key={photo.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="aspect-[4/3] bg-muted flex items-center justify-center">
                  <span className="text-6xl">📷</span>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">
                      {new Date(photo.date).toLocaleDateString('zh-CN')}
                    </span>
                    <span className="px-2 py-1 bg-secondary text-secondary-foreground text-xs rounded-md">
                      {getCategoryName(photo.category)}
                    </span>
                  </div>
                  <h3 className="text-xl font-semibold text-foreground mb-2">
                    {photo.title}
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    {photo.description}
                  </p>
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <div className="flex justify-between">
                      <span>相机:</span>
                      <span>{photo.camera}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>镜头:</span>
                      <span>{photo.lens}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>参数:</span>
                      <span>
                        {photo.settings.aperture} • {photo.settings.shutter} • ISO {photo.settings.iso}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>地点:</span>
                      <span>{photo.location}</span>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-4">
                    {photo.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      )}

      {/* Photo Gallery */}
      <section className="py-8">
        <h2 className="text-2xl font-bold text-foreground mb-6">作品集</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {allPhotos.map((photo) => (
            <Card key={photo.id} className="overflow-hidden hover:shadow-md transition-shadow group cursor-pointer">
              <div className="aspect-square bg-muted flex items-center justify-center relative overflow-hidden">
                <span className="text-4xl">📸</span>
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity text-white text-center">
                    <h4 className="font-semibold mb-1">{photo.title}</h4>
                    <p className="text-sm">{getCategoryName(photo.category)}</p>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </section>

      {/* Equipment Section */}
      <section className="py-12 bg-muted/30 rounded-2xl mt-12">
        <div className="max-w-4xl mx-auto text-center px-8">
          <h2 className="text-2xl font-bold text-foreground mb-6">摄影器材</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="font-semibold text-foreground mb-3">相机</h3>
              <ul className="text-muted-foreground space-y-1">
                <li>Canon EOS R5</li>
                <li>Sony A7R IV</li>
                <li>Fujifilm X-T4</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-foreground mb-3">镜头</h3>
              <ul className="text-muted-foreground space-y-1">
                <li>RF 24-70mm f/2.8L</li>
                <li>FE 85mm f/1.4 GM</li>
                <li>XF 16-55mm f/2.8</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-foreground mb-3">配件</h3>
              <ul className="text-muted-foreground space-y-1">
                <li>碳纤维三脚架</li>
                <li>ND滤镜组</li>
                <li>外置闪光灯</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
