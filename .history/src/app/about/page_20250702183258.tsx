import { Metadata } from "next";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { SITE_CONFIG, SKILLS_CONFIG } from "@/lib/constants";

export const metadata: Metadata = {
  title: "关于我",
  description: `了解更多关于${SITE_CONFIG.author.name}的信息，包括技能、经历和联系方式`,
};

export default function AboutPage() {
  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Hero Section */}
      <section className="text-center py-12">
        <div className="max-w-3xl mx-auto">
          <div className="w-32 h-32 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-8">
            <span className="text-6xl">👨‍💻</span>
          </div>
          <h1 className="text-4xl font-bold text-foreground mb-4">
            你好，我是 {SITE_CONFIG.author.name}
          </h1>
          <p className="text-xl text-muted-foreground leading-relaxed">
            {SITE_CONFIG.author.bio}
          </p>
        </div>
      </section>

      {/* Skills Section */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-foreground mb-8 text-center">技能专长</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {SKILLS_CONFIG.categories.map((category) => (
              <Card key={category.name}>
                <CardHeader>
                  <CardTitle className="text-xl">{category.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {category.skills.map((skill) => (
                      <div key={skill.name} className="flex items-center justify-between">
                        <span className="text-sm font-medium">{skill.name}</span>
                        <div className="flex items-center space-x-1">
                          {[...Array(4)].map((_, i) => (
                            <div
                              key={i}
                              className={`w-2 h-2 rounded-full ${
                                i < getSkillLevel(skill.level)
                                  ? "bg-primary"
                                  : "bg-muted"
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Experience Section */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-foreground mb-8 text-center">工作经历</h2>
          <div className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>全栈开发工程师</CardTitle>
                <CardDescription>某科技公司 • 2022 - 至今</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                  <li>负责前端React应用的开发和维护</li>
                  <li>参与后端API设计和数据库优化</li>
                  <li>推动团队采用现代化开发工具和流程</li>
                  <li>指导初级开发者，提升团队整体技术水平</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>前端开发工程师</CardTitle>
                <CardDescription>某互联网公司 • 2020 - 2022</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                  <li>开发和维护多个Web应用项目</li>
                  <li>优化页面性能，提升用户体验</li>
                  <li>与设计师和产品经理紧密合作</li>
                  <li>参与技术选型和架构设计</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-12">
        <div className="max-w-2xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-foreground mb-8">联系我</h2>
          <p className="text-muted-foreground mb-8">
            如果你想与我交流技术、合作项目或者只是想聊聊天，欢迎通过以下方式联系我：
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {SITE_CONFIG.author.social.email && (
              <a
                href={`mailto:${SITE_CONFIG.author.social.email}`}
                className="p-4 border rounded-lg hover:bg-accent transition-colors text-center"
              >
                <div className="text-2xl mb-2">📧</div>
                <div className="text-sm font-medium">邮箱</div>
              </a>
            )}
            {SITE_CONFIG.author.social.github && (
              <a
                href={SITE_CONFIG.author.social.github}
                target="_blank"
                rel="noopener noreferrer"
                className="p-4 border rounded-lg hover:bg-accent transition-colors text-center"
              >
                <div className="text-2xl mb-2">🐙</div>
                <div className="text-sm font-medium">GitHub</div>
              </a>
            )}
            {SITE_CONFIG.author.social.twitter && (
              <a
                href={SITE_CONFIG.author.social.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="p-4 border rounded-lg hover:bg-accent transition-colors text-center"
              >
                <div className="text-2xl mb-2">🐦</div>
                <div className="text-sm font-medium">Twitter</div>
              </a>
            )}
            {SITE_CONFIG.author.social.linkedin && (
              <a
                href={SITE_CONFIG.author.social.linkedin}
                target="_blank"
                rel="noopener noreferrer"
                className="p-4 border rounded-lg hover:bg-accent transition-colors text-center"
              >
                <div className="text-2xl mb-2">💼</div>
                <div className="text-sm font-medium">LinkedIn</div>
              </a>
            )}
          </div>
        </div>
      </section>
    </div>
  );
}

function getSkillLevel(level: string): number {
  switch (level) {
    case "beginner":
      return 1;
    case "intermediate":
      return 2;
    case "advanced":
      return 3;
    case "expert":
      return 4;
    default:
      return 1;
  }
}
