import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { SITE_CONFIG } from "@/lib/constants";

export default function Home() {
  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Hero Section */}
      <section className="text-center py-20">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6">
            欢迎来到
            <span className="text-primary block mt-2">{SITE_CONFIG.name}</span>
          </h1>
          <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
            {SITE_CONFIG.description}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/blog">阅读博客</Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link href="/about">了解更多</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-foreground mb-4">探索内容</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            发现技术文章、摄影作品和个人项目
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📝</span>
              </div>
              <CardTitle>技术博客</CardTitle>
              <CardDescription>
                分享前端开发、后端技术和编程经验
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild variant="outline" className="w-full">
                <Link href="/blog">查看文章</Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📷</span>
              </div>
              <CardTitle>摄影作品</CardTitle>
              <CardDescription>
                记录生活中的美好瞬间和风景
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild variant="outline" className="w-full">
                <Link href="/photography">浏览作品</Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💼</span>
              </div>
              <CardTitle>项目作品</CardTitle>
              <CardDescription>
                展示个人项目和开发作品
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild variant="outline" className="w-full">
                <Link href="/portfolio">查看项目</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* About Section */}
      <section className="py-20 bg-muted/30 rounded-2xl">
        <div className="max-w-4xl mx-auto text-center px-8">
          <h2 className="text-3xl font-bold text-foreground mb-6">关于我</h2>
          <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
            {SITE_CONFIG.author.bio}
          </p>
          <Button asChild size="lg">
            <Link href="/about">了解更多</Link>
          </Button>
        </div>
      </section>
    </div>
  );
}
