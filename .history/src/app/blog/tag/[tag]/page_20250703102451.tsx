import { Metada<PERSON> } from "next";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { getPostsByTag, getAllTags, formatDate } from "@/lib/blog";
import { SITE_CONFIG } from "@/lib/constants";

interface TagPageProps {
  params: Promise<{
    tag: string;
  }>;
}

// 生成静态路径
export async function generateStaticParams() {
  const tags = await getAllTags();
  return tags.map((tag) => ({
    tag: encodeURIComponent(tag),
  }));
}

// 生成元数据
export async function generateMetadata({ params }: TagPageProps): Promise<Metadata> {
  const { tag: tagParam } = await params;
  const tag = decodeURIComponent(tagParam);
  const posts = await getPostsByTag(tag);

  return {
    title: `标签: ${tag} | ${SITE_CONFIG.name}`,
    description: `查看所有标记为 "${tag}" 的文章，共 ${posts.length} 篇文章。`,
  };
}

export default async function TagPage({ params }: TagPageProps) {
  const { tag: tagParam } = await params;
  const tag = decodeURIComponent(tagParam);
  const posts = await getPostsByTag(tag);

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Header */}
      <section className="text-center py-12">
        {/* Breadcrumb */}
        <nav className="flex items-center justify-center space-x-2 text-sm text-muted-foreground mb-8">
          <Link href="/" className="hover:text-primary transition-colors">
            首页
          </Link>
          <span>/</span>
          <Link href="/blog" className="hover:text-primary transition-colors">
            博客
          </Link>
          <span>/</span>
          <span className="text-foreground">标签: {tag}</span>
        </nav>

        <div className="max-w-3xl mx-auto">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            标签: <span className="text-primary">#{tag}</span>
          </h1>
          <p className="text-xl text-muted-foreground mb-8">
            找到 {posts.length} 篇相关文章
          </p>
          
          <Button asChild variant="outline">
            <Link href="/blog">← 返回博客</Link>
          </Button>
        </div>
      </section>

      {/* Posts List */}
      {posts.length > 0 ? (
        <section className="py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {posts.map((post) => (
              <Card key={post.slug} className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <CardHeader>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">
                      {formatDate(post.date)}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {post.readingTime} 分钟阅读
                    </span>
                  </div>
                  
                  {post.featured && (
                    <div className="mb-2">
                      <span className="px-2 py-1 bg-primary text-primary-foreground text-xs rounded-full">
                        精选
                      </span>
                    </div>
                  )}
                  
                  <CardTitle className="text-xl hover:text-primary transition-colors">
                    <Link href={`/blog/${post.slug}`}>
                      {post.title}
                    </Link>
                  </CardTitle>
                  <CardDescription className="line-clamp-3">
                    {post.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {post.tags.map((postTag) => (
                      <Link
                        key={postTag}
                        href={`/blog/tag/${encodeURIComponent(postTag)}`}
                        className={`px-2 py-1 text-xs rounded transition-colors ${
                          postTag === tag
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted text-muted-foreground hover:bg-primary hover:text-primary-foreground'
                        }`}
                      >
                        #{postTag}
                      </Link>
                    ))}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      {post.category}
                    </span>
                    <Button asChild variant="ghost" size="sm">
                      <Link href={`/blog/${post.slug}`}>
                        阅读 →
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      ) : (
        <section className="py-12 text-center">
          <div className="max-w-md mx-auto">
            <div className="text-6xl mb-4">📝</div>
            <h2 className="text-2xl font-bold text-foreground mb-4">暂无文章</h2>
            <p className="text-muted-foreground mb-8">
              标签 "{tag}" 下还没有文章，请查看其他标签或返回博客首页。
            </p>
            <Button asChild>
              <Link href="/blog">浏览所有文章</Link>
            </Button>
          </div>
        </section>
      )}
    </div>
  );
}
