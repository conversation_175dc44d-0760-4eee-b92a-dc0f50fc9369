import { Metadata } from "next";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { SITE_CONFIG } from "@/lib/constants";
import { getAllPosts, getFeaturedPosts, getAllTags, getAllCategories, formatDate } from "@/lib/blog";

export const metadata: Metadata = {
  title: "博客",
  description: "阅读最新的技术文章和开发经验分享",
};

// 模拟博客文章数据
const mockPosts = [
  {
    slug: "getting-started-with-nextjs-15",
    title: "Next.js 15 新特性详解",
    description: "深入了解 Next.js 15 的新功能，包括 App Router、Server Components 等重要更新。",
    date: "2024-12-28",
    tags: ["Next.js", "React", "前端开发"],
    readingTime: 8,
    featured: true,
  },
  {
    slug: "tailwindcss-4-whats-new",
    title: "TailwindCSS 4.0 全新体验",
    description: "探索 TailwindCSS 4.0 的新特性，了解如何提升开发效率和用户体验。",
    date: "2024-12-25",
    tags: ["TailwindCSS", "CSS", "设计系统"],
    readingTime: 6,
    featured: true,
  },
  {
    slug: "typescript-best-practices",
    title: "TypeScript 最佳实践指南",
    description: "分享在实际项目中使用 TypeScript 的经验和最佳实践。",
    date: "2024-12-20",
    tags: ["TypeScript", "JavaScript", "最佳实践"],
    readingTime: 10,
    featured: false,
  },
  {
    slug: "react-performance-optimization",
    title: "React 性能优化技巧",
    description: "学习如何优化 React 应用的性能，提升用户体验。",
    date: "2024-12-15",
    tags: ["React", "性能优化", "前端开发"],
    readingTime: 12,
    featured: false,
  },
  {
    slug: "modern-css-techniques",
    title: "现代 CSS 技术探索",
    description: "探索现代 CSS 的新特性和技术，包括 Grid、Flexbox、CSS Variables 等。",
    date: "2024-12-10",
    tags: ["CSS", "前端开发", "布局"],
    readingTime: 7,
    featured: false,
  },
];

export default function BlogPage() {
  const featuredPosts = mockPosts.filter(post => post.featured);
  const regularPosts = mockPosts.filter(post => !post.featured);

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Header */}
      <section className="text-center py-12">
        <h1 className="text-4xl font-bold text-foreground mb-4">技术博客</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          分享前端开发经验、技术思考和学习心得
        </p>
      </section>

      {/* Featured Posts */}
      {featuredPosts.length > 0 && (
        <section className="py-8">
          <h2 className="text-2xl font-bold text-foreground mb-6">精选文章</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {featuredPosts.map((post) => (
              <Card key={post.slug} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">
                      {new Date(post.date).toLocaleDateString('zh-CN')}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {post.readingTime} 分钟阅读
                    </span>
                  </div>
                  <CardTitle className="text-xl hover:text-primary transition-colors">
                    <Link href={`/blog/${post.slug}`}>
                      {post.title}
                    </Link>
                  </CardTitle>
                  <CardDescription className="line-clamp-2">
                    {post.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {post.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-secondary text-secondary-foreground text-xs rounded-md"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                  <Button asChild variant="outline" size="sm">
                    <Link href={`/blog/${post.slug}`}>阅读全文</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      )}

      {/* All Posts */}
      <section className="py-8">
        <h2 className="text-2xl font-bold text-foreground mb-6">所有文章</h2>
        <div className="space-y-6">
          {regularPosts.map((post) => (
            <Card key={post.slug} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex flex-col md:flex-row md:items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-4 mb-2">
                      <span className="text-sm text-muted-foreground">
                        {new Date(post.date).toLocaleDateString('zh-CN')}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        {post.readingTime} 分钟阅读
                      </span>
                    </div>
                    <h3 className="text-xl font-semibold text-foreground mb-2 hover:text-primary transition-colors">
                      <Link href={`/blog/${post.slug}`}>
                        {post.title}
                      </Link>
                    </h3>
                    <p className="text-muted-foreground mb-3 line-clamp-2">
                      {post.description}
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {post.tags.map((tag) => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-secondary text-secondary-foreground text-xs rounded-md"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="mt-4 md:mt-0 md:ml-6">
                    <Button asChild variant="outline">
                      <Link href={`/blog/${post.slug}`}>阅读</Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-12 bg-muted/30 rounded-2xl mt-12">
        <div className="max-w-2xl mx-auto text-center px-8">
          <h2 className="text-2xl font-bold text-foreground mb-4">订阅更新</h2>
          <p className="text-muted-foreground mb-6">
            获取最新的技术文章和开发经验分享
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="输入你的邮箱"
              className="flex-1 px-4 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            />
            <Button>订阅</Button>
          </div>
        </div>
      </section>
    </div>
  );
}
