import { Metadata } from "next";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { SearchBox } from "@/components/blog/SearchBox";
import { searchPosts, formatDate } from "@/lib/blog";
import { SITE_CONFIG } from "@/lib/constants";

interface SearchPageProps {
  searchParams: Promise<{
    q?: string;
  }>;
}

// 生成元数据
export async function generateMetadata({ searchParams }: SearchPageProps): Promise<Metadata> {
  const { q } = await searchParams;
  const query = q || '';
  
  if (!query) {
    return {
      title: `搜索 | ${SITE_CONFIG.name}`,
      description: "搜索博客文章",
    };
  }

  const results = await searchPosts(query);
  
  return {
    title: `搜索: ${query} | ${SITE_CONFIG.name}`,
    description: `搜索 "${query}" 的结果，找到 ${results.length} 篇相关文章。`,
  };
}

export default async function SearchPage({ searchParams }: SearchPageProps) {
  const query = searchParams.q || '';
  const results = query ? await searchPosts(query) : [];

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Header */}
      <section className="text-center py-12">
        {/* Breadcrumb */}
        <nav className="flex items-center justify-center space-x-2 text-sm text-muted-foreground mb-8">
          <Link href="/" className="hover:text-primary transition-colors">
            首页
          </Link>
          <span>/</span>
          <Link href="/blog" className="hover:text-primary transition-colors">
            博客
          </Link>
          <span>/</span>
          <span className="text-foreground">搜索</span>
        </nav>

        <div className="max-w-3xl mx-auto">
          <h1 className="text-4xl font-bold text-foreground mb-8">搜索文章</h1>
          
          {/* Search Box */}
          <div className="mb-8">
            <SearchBox className="max-w-md mx-auto" />
          </div>

          {query && (
            <div className="mb-8">
              <p className="text-xl text-muted-foreground">
                搜索 "<span className="text-primary font-medium">{query}</span>" 的结果
              </p>
              <p className="text-muted-foreground mt-2">
                找到 {results.length} 篇相关文章
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Search Results */}
      {query ? (
        results.length > 0 ? (
          <section className="py-12">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {results.map((post) => (
                <Card key={post.slug} className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-muted-foreground">
                        {formatDate(post.date)}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        {post.readingTime} 分钟阅读
                      </span>
                    </div>
                    
                    {post.featured && (
                      <div className="mb-2">
                        <span className="px-2 py-1 bg-primary text-primary-foreground text-xs rounded-full">
                          精选
                        </span>
                      </div>
                    )}
                    
                    <CardTitle className="text-xl hover:text-primary transition-colors">
                      <Link href={`/blog/${post.slug}`}>
                        {post.title}
                      </Link>
                    </CardTitle>
                    <CardDescription className="line-clamp-3">
                      {post.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {post.tags.slice(0, 3).map((tag) => (
                        <Link
                          key={tag}
                          href={`/blog/tag/${encodeURIComponent(tag)}`}
                          className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded hover:bg-primary hover:text-primary-foreground transition-colors"
                        >
                          #{tag}
                        </Link>
                      ))}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        {post.category}
                      </span>
                      <Button asChild variant="ghost" size="sm">
                        <Link href={`/blog/${post.slug}`}>
                          阅读 →
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        ) : (
          <section className="py-12 text-center">
            <div className="max-w-md mx-auto">
              <div className="text-6xl mb-4">🔍</div>
              <h2 className="text-2xl font-bold text-foreground mb-4">未找到相关文章</h2>
              <p className="text-muted-foreground mb-8">
                没有找到包含 "{query}" 的文章，请尝试其他关键词或浏览所有文章。
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild variant="outline">
                  <Link href="/blog">浏览所有文章</Link>
                </Button>
                <SearchBox placeholder="尝试其他关键词..." />
              </div>
            </div>
          </section>
        )
      ) : (
        <section className="py-12 text-center">
          <div className="max-w-md mx-auto">
            <div className="text-6xl mb-4">📝</div>
            <h2 className="text-2xl font-bold text-foreground mb-4">开始搜索</h2>
            <p className="text-muted-foreground mb-8">
              输入关键词来搜索相关的技术文章和开发经验分享。
            </p>
            <Button asChild>
              <Link href="/blog">浏览所有文章</Link>
            </Button>
          </div>
        </section>
      )}
    </div>
  );
}
