"use client";

import * as React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { ThemeSelector } from "@/components/ui/ThemeToggle";
import { useTheme } from "@/components/providers/ThemeProvider";

export default function ThemeSettingsPage() {
  const { theme, resolvedTheme, systemTheme } = useTheme();

  return (
    <div className="container mx-auto px-4 py-12 max-w-4xl">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-foreground mb-2">主题设置</h1>
        <p className="text-muted-foreground">
          自定义您的浏览体验，选择适合您的主题模式
        </p>
      </div>

      {/* 主题选择器 */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🎨 主题模式
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <p className="text-sm text-muted-foreground mb-4">
              选择您偏好的主题模式。系统模式会根据您的设备设置自动切换。
            </p>
            <ThemeSelector />
          </div>

          {/* 当前状态显示 */}
          <div className="bg-muted/50 rounded-lg p-4">
            <h4 className="font-semibold text-foreground mb-3">当前状态</h4>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">选择的主题:</span>
                <div className="font-medium text-foreground">
                  {theme === "light" && "☀️ 浅色模式"}
                  {theme === "dark" && "🌙 深色模式"}
                  {theme === "system" && "💻 跟随系统"}
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">系统主题:</span>
                <div className="font-medium text-foreground">
                  {systemTheme === "light" ? "☀️ 浅色" : "🌙 深色"}
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">实际显示:</span>
                <div className="font-medium text-foreground">
                  {resolvedTheme === "light" ? "☀️ 浅色模式" : "🌙 深色模式"}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 主题预览 */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            👀 主题预览
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <p className="text-sm text-muted-foreground">
              预览不同主题模式下的界面效果
            </p>

            {/* 浅色主题预览 */}
            <div className="border rounded-lg overflow-hidden">
              <div className="bg-white text-gray-900 p-4">
                <div className="flex items-center justify-between mb-3">
                  <h5 className="font-semibold">☀️ 浅色模式</h5>
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    预览
                  </span>
                </div>
                <div className="space-y-2">
                  <div className="h-2 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-2 bg-gray-200 rounded w-1/2"></div>
                  <div className="flex gap-2 mt-3">
                    <div className="h-8 bg-gray-900 text-white px-3 rounded text-xs flex items-center">
                      按钮
                    </div>
                    <div className="h-8 bg-gray-100 text-gray-900 px-3 rounded text-xs flex items-center border">
                      次要按钮
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 深色主题预览 */}
            <div className="border rounded-lg overflow-hidden">
              <div className="bg-gray-900 text-gray-100 p-4">
                <div className="flex items-center justify-between mb-3">
                  <h5 className="font-semibold">🌙 深色模式</h5>
                  <span className="text-xs bg-gray-800 text-gray-300 px-2 py-1 rounded">
                    预览
                  </span>
                </div>
                <div className="space-y-2">
                  <div className="h-2 bg-gray-700 rounded w-3/4"></div>
                  <div className="h-2 bg-gray-700 rounded w-1/2"></div>
                  <div className="flex gap-2 mt-3">
                    <div className="h-8 bg-gray-100 text-gray-900 px-3 rounded text-xs flex items-center">
                      按钮
                    </div>
                    <div className="h-8 bg-gray-800 text-gray-100 px-3 rounded text-xs flex items-center border border-gray-700">
                      次要按钮
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 主题说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            💡 主题说明
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div>
              <h5 className="font-semibold text-foreground mb-2">☀️ 浅色模式</h5>
              <p className="text-muted-foreground">
                经典的浅色界面，适合在光线充足的环境中使用。提供清晰的对比度和良好的可读性。
              </p>
            </div>
            
            <div>
              <h5 className="font-semibold text-foreground mb-2">🌙 深色模式</h5>
              <p className="text-muted-foreground">
                深色界面减少眼部疲劳，特别适合在低光环境或夜间使用。同时也更加节能。
              </p>
            </div>
            
            <div>
              <h5 className="font-semibold text-foreground mb-2">💻 跟随系统</h5>
              <p className="text-muted-foreground">
                自动根据您的操作系统设置切换主题。当系统切换到深色模式时，网站也会自动切换。
              </p>
            </div>

            <div className="bg-muted/50 rounded-lg p-4 mt-6">
              <h5 className="font-semibold text-foreground mb-2">💾 自动保存</h5>
              <p className="text-muted-foreground">
                您的主题偏好会自动保存到浏览器本地存储中，下次访问时会自动应用您的设置。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 返回按钮 */}
      <div className="mt-8 text-center">
        <Button 
          variant="outline" 
          onClick={() => window.history.back()}
        >
          ← 返回上一页
        </Button>
      </div>
    </div>
  );
}
