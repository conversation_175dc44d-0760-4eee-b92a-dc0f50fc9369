import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { SITE_CONFIG } from "@/lib/constants";

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32 relative">
          <div className="max-w-4xl mx-auto text-center">
            {/* Avatar */}
            <div className="w-32 h-32 mx-auto mb-8 relative">
              <div className="w-full h-full bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-4xl font-bold text-primary-foreground shadow-2xl">
                Z
              </div>
              <div className="absolute -inset-4 bg-gradient-to-r from-primary/20 to-transparent rounded-full blur-xl animate-pulse"></div>
            </div>

            {/* Main Title */}
            <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold text-foreground mb-6 leading-tight">
              你好，我是
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary/60 block mt-2">
                {SITE_CONFIG.author.name}
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl sm:text-2xl text-muted-foreground mb-4 font-medium">
              全栈开发工程师 • 技术博主 • 摄影爱好者
            </p>

            {/* Description */}
            <p className="text-lg text-muted-foreground mb-12 leading-relaxed max-w-2xl mx-auto">
              {SITE_CONFIG.description}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <Button asChild size="lg" className="text-lg px-8 py-6 shadow-lg hover:shadow-xl transition-all">
                <Link href="/blog">
                  <span className="mr-2">📚</span>
                  阅读博客
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="text-lg px-8 py-6 shadow-lg hover:shadow-xl transition-all">
                <Link href="/about">
                  <span className="mr-2">👋</span>
                  了解更多
                </Link>
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">50+</div>
                <div className="text-sm text-muted-foreground">技术文章</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">20+</div>
                <div className="text-sm text-muted-foreground">开源项目</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">100+</div>
                <div className="text-sm text-muted-foreground">摄影作品</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">3年+</div>
                <div className="text-sm text-muted-foreground">开发经验</div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-muted-foreground/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-muted-foreground/30 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-foreground mb-4">探索内容</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            发现技术文章、摄影作品和个人项目
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📝</span>
              </div>
              <CardTitle>技术博客</CardTitle>
              <CardDescription>
                分享前端开发、后端技术和编程经验
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild variant="outline" className="w-full">
                <Link href="/blog">查看文章</Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📷</span>
              </div>
              <CardTitle>摄影作品</CardTitle>
              <CardDescription>
                记录生活中的美好瞬间和风景
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild variant="outline" className="w-full">
                <Link href="/photography">浏览作品</Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💼</span>
              </div>
              <CardTitle>项目作品</CardTitle>
              <CardDescription>
                展示个人项目和开发作品
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild variant="outline" className="w-full">
                <Link href="/portfolio">查看项目</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* About Section */}
      <section className="py-20 bg-muted/30 rounded-2xl">
        <div className="max-w-4xl mx-auto text-center px-8">
          <h2 className="text-3xl font-bold text-foreground mb-6">关于我</h2>
          <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
            {SITE_CONFIG.author.bio}
          </p>
          <Button asChild size="lg">
            <Link href="/about">了解更多</Link>
          </Button>
        </div>
      </section>
    </div>
  );
}
