import { Photo, PhotoCategory } from '@/types';
import { PHOTOGRAPHY_CONFIG } from '@/lib/constants';

// 示例摄影作品数据
const samplePhotos: Photo[] = [
  {
    id: "sunset-landscape-1",
    title: "夕阳西下",
    description: "在山顶拍摄的日落美景，金色的阳光洒在山峦之间，形成了绝美的光影效果。",
    src: "/images/photography/sunset-1.jpg",
    alt: "夕阳西下的山景",
    category: "landscape",
    tags: ["日落", "山景", "自然", "风光"],
    date: "2024-12-20",
    camera: "Canon EOS R5",
    lens: "RF 24-70mm f/2.8L IS USM",
    settings: {
      aperture: "f/8",
      shutter: "1/125s",
      iso: "100",
      focalLength: "35mm",
    },
    location: "黄山",
    featured: true,
  },
  {
    id: "street-portrait-1",
    title: "街头人像",
    description: "捕捉城市中的人文瞬间，记录都市生活的真实写照。",
    src: "/images/photography/street-1.jpg",
    alt: "街头人像摄影",
    category: "street",
    tags: ["人像", "街拍", "城市", "人文"],
    date: "2024-12-15",
    camera: "Sony A7R IV",
    lens: "FE 85mm f/1.4 GM",
    settings: {
      aperture: "f/2.8",
      shutter: "1/250s",
      iso: "400",
      focalLength: "85mm",
    },
    location: "上海",
    featured: true,
  },
  {
    id: "architecture-1",
    title: "现代建筑",
    description: "城市建筑的几何美学，展现现代设计的简约与力量。",
    src: "/images/photography/architecture-1.jpg",
    alt: "现代建筑摄影",
    category: "architecture",
    tags: ["建筑", "几何", "现代", "设计"],
    date: "2024-12-10",
    camera: "Fujifilm X-T4",
    lens: "XF 16-55mm f/2.8 R LM WR",
    settings: {
      aperture: "f/5.6",
      shutter: "1/200s",
      iso: "200",
      focalLength: "24mm",
    },
    location: "深圳",
    featured: false,
  },
  {
    id: "nature-macro-1",
    title: "花卉微距",
    description: "春天花朵的细节之美，微距镜头下的自然奇迹。",
    src: "/images/photography/macro-1.jpg",
    alt: "花卉微距摄影",
    category: "nature",
    tags: ["微距", "花卉", "春天", "细节"],
    date: "2024-12-05",
    camera: "Nikon D850",
    lens: "AF-S VR Micro-NIKKOR 105mm f/2.8G IF-ED",
    settings: {
      aperture: "f/5.6",
      shutter: "1/320s",
      iso: "320",
      focalLength: "105mm",
    },
    location: "杭州西湖",
    featured: false,
  },
  {
    id: "travel-landscape-1",
    title: "海边日出",
    description: "海边的第一缕阳光，温暖的光线唤醒沉睡的大地。",
    src: "/images/photography/travel-1.jpg",
    alt: "海边日出摄影",
    category: "travel",
    tags: ["日出", "海景", "旅行", "风光"],
    date: "2024-11-28",
    camera: "Canon EOS R6",
    lens: "RF 15-35mm f/2.8L IS USM",
    settings: {
      aperture: "f/11",
      shutter: "1/60s",
      iso: "100",
      focalLength: "20mm",
    },
    location: "青岛",
    featured: false,
  },
  {
    id: "portrait-studio-1",
    title: "工作室人像",
    description: "专业灯光下的人像作品，展现人物的内在气质。",
    src: "/images/photography/portrait-1.jpg",
    alt: "工作室人像摄影",
    category: "portrait",
    tags: ["人像", "工作室", "专业", "肖像"],
    date: "2024-11-20",
    camera: "Sony A7 III",
    lens: "FE 70-200mm f/2.8 GM OSS",
    settings: {
      aperture: "f/2.8",
      shutter: "1/160s",
      iso: "200",
      focalLength: "135mm",
    },
    location: "工作室",
    featured: false,
  },
  {
    id: "landscape-mountain-1",
    title: "雪山晨光",
    description: "清晨的雪山在阳光照射下闪闪发光，纯净而壮美。",
    src: "/images/photography/mountain-1.jpg",
    alt: "雪山晨光摄影",
    category: "landscape",
    tags: ["雪山", "晨光", "风光", "自然"],
    date: "2024-11-15",
    camera: "Canon EOS R5",
    lens: "RF 70-200mm f/2.8L IS USM",
    settings: {
      aperture: "f/11",
      shutter: "1/250s",
      iso: "200",
      focalLength: "135mm",
    },
    location: "四川贡嘎山",
    featured: true,
  },
  {
    id: "street-night-1",
    title: "夜色街头",
    description: "城市夜晚的霓虹灯光，营造出迷人的都市氛围。",
    src: "/images/photography/night-1.jpg",
    alt: "夜色街头摄影",
    category: "street",
    tags: ["夜景", "街拍", "霓虹", "城市"],
    date: "2024-11-10",
    camera: "Sony A7S III",
    lens: "FE 24-70mm f/2.8 GM",
    settings: {
      aperture: "f/2.8",
      shutter: "1/60s",
      iso: "1600",
      focalLength: "50mm",
    },
    location: "东京",
    featured: false,
  },
];

// 获取所有照片
export async function getAllPhotos(): Promise<Photo[]> {
  // 在实际项目中，这里可以从文件系统或数据库读取
  // 目前返回示例数据
  return samplePhotos.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
}

// 根据ID获取单张照片
export async function getPhotoById(id: string): Promise<Photo | null> {
  const photos = await getAllPhotos();
  return photos.find(photo => photo.id === id) || null;
}

// 获取精选照片
export async function getFeaturedPhotos(limit?: number): Promise<Photo[]> {
  const photos = await getAllPhotos();
  const featured = photos.filter(photo => photo.featured);
  return limit ? featured.slice(0, limit) : featured;
}

// 根据分类获取照片
export async function getPhotosByCategory(category: string): Promise<Photo[]> {
  const photos = await getAllPhotos();
  return photos.filter(photo => photo.category === category);
}

// 根据标签获取照片
export async function getPhotosByTag(tag: string): Promise<Photo[]> {
  const photos = await getAllPhotos();
  return photos.filter(photo => photo.tags.includes(tag));
}

// 搜索照片
export async function searchPhotos(query: string): Promise<Photo[]> {
  const photos = await getAllPhotos();
  const lowercaseQuery = query.toLowerCase();
  
  return photos.filter(photo => 
    photo.title.toLowerCase().includes(lowercaseQuery) ||
    photo.description?.toLowerCase().includes(lowercaseQuery) ||
    photo.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
    photo.location?.toLowerCase().includes(lowercaseQuery)
  );
}

// 获取相关照片
export async function getRelatedPhotos(photoId: string, limit: number = 4): Promise<Photo[]> {
  const photos = await getAllPhotos();
  const currentPhoto = photos.find(photo => photo.id === photoId);
  
  if (!currentPhoto) return [];
  
  // 优先返回同分类的照片
  const sameCategory = photos.filter(photo => 
    photo.id !== photoId && photo.category === currentPhoto.category
  );
  
  // 如果同分类照片不够，添加其他照片
  if (sameCategory.length < limit) {
    const others = photos.filter(photo => 
      photo.id !== photoId && photo.category !== currentPhoto.category
    );
    return [...sameCategory, ...others].slice(0, limit);
  }
  
  return sameCategory.slice(0, limit);
}

// 获取所有分类
export async function getPhotoCategories(): Promise<PhotoCategory[]> {
  return PHOTOGRAPHY_CONFIG.categories.map(cat => ({
    ...cat,
    coverImage: `/images/photography/category-${cat.id}.jpg`
  }));
}

// 获取照片统计信息
export async function getPhotoStats() {
  const photos = await getAllPhotos();
  const categories = await getPhotoCategories();
  
  const categoryStats = categories.map(category => ({
    ...category,
    count: photos.filter(photo => photo.category === category.id).length
  }));
  
  return {
    totalPhotos: photos.length,
    featuredPhotos: photos.filter(photo => photo.featured).length,
    categories: categoryStats,
    latestPhoto: photos[0],
  };
}

// 格式化日期
export function formatPhotoDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

// 生成照片的不同尺寸URL
export function getPhotoSizes(src: string) {
  const basePath = src.replace(/\.[^/.]+$/, '');
  const extension = src.split('.').pop();
  
  return {
    thumbnail: `${basePath}-thumb.${extension}`,
    medium: `${basePath}-medium.${extension}`,
    large: `${basePath}-large.${extension}`,
    original: src,
  };
}
