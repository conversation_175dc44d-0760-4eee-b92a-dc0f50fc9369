import { SITE_CONFIG, BLOG_CONFIG } from '@/lib/constants';
import { getAllPosts } from '@/lib/blog';
import { BlogPostMeta } from '@/types';

interface RSSItem {
  title: string;
  description: string;
  link: string;
  guid: string;
  pubDate: string;
  author: string;
  category?: string[];
}

interface RSSFeed {
  title: string;
  description: string;
  link: string;
  language: string;
  lastBuildDate: string;
  generator: string;
  items: RSSItem[];
}

/**
 * 生成 RSS XML 内容
 */
export function generateRSSXML(feed: RSSFeed): string {
  const { title, description, link, language, lastBuildDate, generator, items } = feed;

  const rssItems = items
    .map(
      (item) => `
    <item>
      <title><![CDATA[${item.title}]]></title>
      <description><![CDATA[${item.description}]]></description>
      <link>${item.link}</link>
      <guid isPermaLink="true">${item.guid}</guid>
      <pubDate>${item.pubDate}</pubDate>
      <author>${item.author}</author>
      ${item.category ? item.category.map(cat => `<category><![CDATA[${cat}]]></category>`).join('\n      ') : ''}
    </item>`
    )
    .join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title><![CDATA[${title}]]></title>
    <description><![CDATA[${description}]]></description>
    <link>${link}</link>
    <language>${language}</language>
    <lastBuildDate>${lastBuildDate}</lastBuildDate>
    <generator>${generator}</generator>
    <atom:link href="${link}/rss.xml" rel="self" type="application/rss+xml"/>
    ${rssItems}
  </channel>
</rss>`;
}

/**
 * 生成博客 RSS 订阅
 */
export async function generateBlogRSS(): Promise<string> {
  const posts = await getAllPosts();
  const latestPosts = posts.slice(0, 20); // 只包含最新的20篇文章

  const rssItems: RSSItem[] = latestPosts.map((post: BlogPostMeta) => ({
    title: post.title,
    description: post.description,
    link: `${SITE_CONFIG.url}/blog/${post.slug}`,
    guid: `${SITE_CONFIG.url}/blog/${post.slug}`,
    pubDate: new Date(post.date).toUTCString(),
    author: `${SITE_CONFIG.author.email} (${SITE_CONFIG.author.name})`,
    category: post.tags || [],
  }));

  const feed: RSSFeed = {
    title: `${SITE_CONFIG.name} - 博客`,
    description: `${SITE_CONFIG.author.name}的技术博客，分享前端开发、编程技巧和技术思考`,
    link: SITE_CONFIG.url,
    language: 'zh-CN',
    lastBuildDate: new Date().toUTCString(),
    generator: 'Next.js RSS Generator',
    items: rssItems,
  };

  return generateRSSXML(feed);
}

/**
 * 生成 Atom 订阅格式
 */
export function generateAtomXML(feed: RSSFeed): string {
  const { title, description, link, lastBuildDate, items } = feed;

  const atomEntries = items
    .map(
      (item) => `
  <entry>
    <title type="html"><![CDATA[${item.title}]]></title>
    <link href="${item.link}"/>
    <id>${item.guid}</id>
    <updated>${new Date(item.pubDate).toISOString()}</updated>
    <summary type="html"><![CDATA[${item.description}]]></summary>
    <author>
      <name>${SITE_CONFIG.author.name}</name>
      <email>${SITE_CONFIG.author.email}</email>
    </author>
    ${item.category ? item.category.map(cat => `<category term="${cat}"/>`).join('\n    ') : ''}
  </entry>`
    )
    .join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<feed xmlns="http://www.w3.org/2005/Atom">
  <title><![CDATA[${title}]]></title>
  <subtitle><![CDATA[${description}]]></subtitle>
  <link href="${link}"/>
  <link href="${link}/atom.xml" rel="self"/>
  <id>${link}/</id>
  <updated>${new Date(lastBuildDate).toISOString()}</updated>
  <generator uri="https://nextjs.org/" version="15.0">Next.js</generator>
  ${atomEntries}
</feed>`;
}

/**
 * 生成博客 Atom 订阅
 */
export async function generateBlogAtom(): Promise<string> {
  const posts = await getAllPosts();
  const latestPosts = posts.slice(0, 20);

  const rssItems: RSSItem[] = latestPosts.map((post: BlogPostMeta) => ({
    title: post.title,
    description: post.description,
    link: `${SITE_CONFIG.url}/blog/${post.slug}`,
    guid: `${SITE_CONFIG.url}/blog/${post.slug}`,
    pubDate: new Date(post.date).toUTCString(),
    author: `${SITE_CONFIG.author.email} (${SITE_CONFIG.author.name})`,
    category: post.tags || [],
  }));

  const feed: RSSFeed = {
    title: `${SITE_CONFIG.name} - 博客`,
    description: `${SITE_CONFIG.author.name}的技术博客，分享前端开发、编程技巧和技术思考`,
    link: SITE_CONFIG.url,
    language: 'zh-CN',
    lastBuildDate: new Date().toUTCString(),
    generator: 'Next.js Atom Generator',
    items: rssItems,
  };

  return generateAtomXML(feed);
}

/**
 * 生成 JSON Feed 格式
 */
export function generateJSONFeed(feed: RSSFeed): string {
  const { title, description, link, items } = feed;

  const jsonItems = items.map((item) => ({
    id: item.guid,
    url: item.link,
    title: item.title,
    content_html: item.description,
    summary: item.description,
    date_published: new Date(item.pubDate).toISOString(),
    author: {
      name: SITE_CONFIG.author.name,
      email: SITE_CONFIG.author.email,
    },
    tags: item.category || [],
  }));

  const jsonFeed = {
    version: 'https://jsonfeed.org/version/1.1',
    title,
    description,
    home_page_url: link,
    feed_url: `${link}/feed.json`,
    language: 'zh-CN',
    author: {
      name: SITE_CONFIG.author.name,
      email: SITE_CONFIG.author.email,
      url: link,
    },
    items: jsonItems,
  };

  return JSON.stringify(jsonFeed, null, 2);
}

/**
 * 生成博客 JSON Feed
 */
export async function generateBlogJSONFeed(): Promise<string> {
  const posts = await getAllPosts();
  const latestPosts = posts.slice(0, 20);

  const rssItems: RSSItem[] = latestPosts.map((post: BlogPostMeta) => ({
    title: post.title,
    description: post.description,
    link: `${SITE_CONFIG.url}/blog/${post.slug}`,
    guid: `${SITE_CONFIG.url}/blog/${post.slug}`,
    pubDate: new Date(post.date).toUTCString(),
    author: `${SITE_CONFIG.author.email} (${SITE_CONFIG.author.name})`,
    category: post.tags || [],
  }));

  const feed: RSSFeed = {
    title: `${SITE_CONFIG.name} - 博客`,
    description: `${SITE_CONFIG.author.name}的技术博客，分享前端开发、编程技巧和技术思考`,
    link: SITE_CONFIG.url,
    language: 'zh-CN',
    lastBuildDate: new Date().toUTCString(),
    generator: 'Next.js JSON Feed Generator',
    items: rssItems,
  };

  return generateJSONFeed(feed);
}

/**
 * 生成站点地图 XML
 */
export async function generateSitemap(): Promise<string> {
  const posts = await getAllPosts();
  const currentDate = new Date().toISOString();

  // 静态页面
  const staticPages = [
    { url: '', changefreq: 'daily', priority: '1.0' },
    { url: '/about', changefreq: 'monthly', priority: '0.8' },
    { url: '/blog', changefreq: 'daily', priority: '0.9' },
    { url: '/portfolio', changefreq: 'monthly', priority: '0.8' },
    { url: '/photography', changefreq: 'weekly', priority: '0.7' },
  ];

  // 博客文章页面
  const blogPages = posts.map((post: BlogPostMeta) => ({
    url: `/blog/${post.slug}`,
    changefreq: 'monthly',
    priority: '0.6',
    lastmod: new Date(post.date).toISOString(),
  }));

  // 标签页面
  const allTags = [...new Set(posts.flatMap((post: BlogPost) => post.tags || []))];
  const tagPages = allTags.map((tag) => ({
    url: `/blog/tag/${encodeURIComponent(tag)}`,
    changefreq: 'weekly',
    priority: '0.5',
  }));

  const allPages = [...staticPages, ...blogPages, ...tagPages];

  const sitemapXML = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allPages
  .map(
    (page) => `  <url>
    <loc>${SITE_CONFIG.url}${page.url}</loc>
    <lastmod>${page.lastmod || currentDate}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`
  )
  .join('\n')}
</urlset>`;

  return sitemapXML;
}

/**
 * 生成 robots.txt 内容
 */
export function generateRobotsTxt(): string {
  return `User-agent: *
Allow: /

# Sitemaps
Sitemap: ${SITE_CONFIG.url}/sitemap.xml

# Disallow admin and private areas
Disallow: /admin/
Disallow: /api/
Disallow: /_next/
Disallow: /private/

# Allow specific bots
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

# Crawl delay
Crawl-delay: 1`;
}
