import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { remark } from 'remark';
import remarkGfm from 'remark-gfm';
import remarkHtml from 'remark-html';
import { BlogPost, BlogPostMeta } from '@/types';

const postsDirectory = path.join(process.cwd(), 'content/blog');

// 获取所有博客文章的元数据
export async function getAllPosts(): Promise<BlogPostMeta[]> {
  try {
    const fileNames = fs.readdirSync(postsDirectory);
    const allPostsData = await Promise.all(
      fileNames
        .filter(fileName => fileName.endsWith('.md'))
        .map(async (fileName) => {
          const slug = fileName.replace(/\.md$/, '');
          const fullPath = path.join(postsDirectory, fileName);
          const fileContents = fs.readFileSync(fullPath, 'utf8');
          const { data } = matter(fileContents);

          return {
            slug,
            title: data.title || '',
            description: data.description || '',
            date: data.date || '',
            tags: data.tags || [],
            category: data.category || '',
            author: data.author || '',
            featured: data.featured || false,
            readingTime: data.readingTime || 0,
          } as BlogPostMeta;
        })
    );

    // 按日期排序（最新的在前）
    return allPostsData.sort((a, b) => {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    });
  } catch (error) {
    console.error('Error reading blog posts:', error);
    return [];
  }
}

// 获取特定博客文章的完整内容
export async function getPostBySlug(slug: string): Promise<BlogPost | null> {
  try {
    const fullPath = path.join(postsDirectory, `${slug}.md`);
    
    if (!fs.existsSync(fullPath)) {
      return null;
    }

    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const { data, content } = matter(fileContents);

    // 处理 Markdown 内容
    const processedContent = await remark()
      .use(remarkGfm)
      .use(remarkHtml, { sanitize: false })
      .process(content);

    const contentHtml = processedContent.toString();

    return {
      slug,
      title: data.title || '',
      description: data.description || '',
      date: data.date || '',
      tags: data.tags || [],
      category: data.category || '',
      author: data.author || '',
      featured: data.featured || false,
      readingTime: data.readingTime || 0,
      content: contentHtml,
    };
  } catch (error) {
    console.error(`Error reading post ${slug}:`, error);
    return null;
  }
}

// 获取精选文章
export async function getFeaturedPosts(): Promise<BlogPostMeta[]> {
  const allPosts = await getAllPosts();
  return allPosts.filter(post => post.featured);
}

// 根据标签获取文章
export async function getPostsByTag(tag: string): Promise<BlogPostMeta[]> {
  const allPosts = await getAllPosts();
  return allPosts.filter(post => 
    post.tags.some(postTag => 
      postTag.toLowerCase() === tag.toLowerCase()
    )
  );
}

// 根据分类获取文章
export async function getPostsByCategory(category: string): Promise<BlogPostMeta[]> {
  const allPosts = await getAllPosts();
  return allPosts.filter(post =>
    post.category?.toLowerCase() === category.toLowerCase()
  );
}

// 获取所有标签
export async function getAllTags(): Promise<string[]> {
  const allPosts = await getAllPosts();
  const tagSet = new Set<string>();
  
  allPosts.forEach(post => {
    post.tags.forEach(tag => tagSet.add(tag));
  });
  
  return Array.from(tagSet).sort();
}

// 获取所有分类
export async function getAllCategories(): Promise<string[]> {
  const allPosts = await getAllPosts();
  const categorySet = new Set<string>();
  
  allPosts.forEach(post => {
    if (post.category) {
      categorySet.add(post.category);
    }
  });
  
  return Array.from(categorySet).sort();
}

// 搜索文章
export async function searchPosts(query: string): Promise<BlogPostMeta[]> {
  const allPosts = await getAllPosts();
  const searchQuery = query.toLowerCase();
  
  return allPosts.filter(post =>
    post.title.toLowerCase().includes(searchQuery) ||
    post.description.toLowerCase().includes(searchQuery) ||
    post.tags.some(tag => tag.toLowerCase().includes(searchQuery)) ||
    post.category?.toLowerCase().includes(searchQuery)
  );
}

// 获取相关文章
export async function getRelatedPosts(currentSlug: string, limit: number = 3): Promise<BlogPostMeta[]> {
  const allPosts = await getAllPosts();
  const currentPost = allPosts.find(post => post.slug === currentSlug);
  
  if (!currentPost) {
    return [];
  }
  
  // 根据标签和分类计算相关性
  const relatedPosts = allPosts
    .filter(post => post.slug !== currentSlug)
    .map(post => {
      let score = 0;
      
      // 相同分类加分
      if (post.category === currentPost.category) {
        score += 3;
      }
      
      // 相同标签加分
      const commonTags = post.tags.filter(tag => 
        currentPost.tags.includes(tag)
      );
      score += commonTags.length * 2;
      
      return { ...post, score };
    })
    .filter(post => post.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, limit);
  
  return relatedPosts.map(({ score, ...post }) => post);
}

// 获取文章统计信息
export async function getBlogStats() {
  const allPosts = await getAllPosts();
  const allTags = await getAllTags();
  const allCategories = await getAllCategories();
  
  return {
    totalPosts: allPosts.length,
    featuredPosts: allPosts.filter(post => post.featured).length,
    totalTags: allTags.length,
    totalCategories: allCategories.length,
    averageReadingTime: Math.round(
      allPosts.reduce((sum, post) => sum + post.readingTime, 0) / allPosts.length
    ),
  };
}

// 生成文章的阅读时间（基于内容长度估算）
export function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200; // 平均阅读速度
  const words = content.split(/\s+/).length;
  return Math.ceil(words / wordsPerMinute);
}

// 格式化日期
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

// 生成文章摘要
export function generateExcerpt(content: string, maxLength: number = 150): string {
  // 移除 HTML 标签
  const plainText = content.replace(/<[^>]*>/g, '');
  
  if (plainText.length <= maxLength) {
    return plainText;
  }
  
  return plainText.substring(0, maxLength).trim() + '...';
}
