"use client";

import * as React from "react";

interface UseLazyLoadingOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
}

export const useLazyLoading = (options: UseLazyLoadingOptions = {}) => {
  const {
    threshold = 0.1,
    rootMargin = "50px",
    triggerOnce = true,
  } = options;

  const [isIntersecting, setIsIntersecting] = React.useState(false);
  const [hasTriggered, setHasTriggered] = React.useState(false);
  const elementRef = React.useRef<HTMLElement>(null);

  React.useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // 如果已经触发过且设置了只触发一次，则不再观察
    if (hasTriggered && triggerOnce) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        const isVisible = entry.isIntersecting;
        setIsIntersecting(isVisible);
        
        if (isVisible && !hasTriggered) {
          setHasTriggered(true);
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [threshold, rootMargin, hasTriggered, triggerOnce]);

  return {
    elementRef,
    isIntersecting,
    hasTriggered,
    isVisible: triggerOnce ? hasTriggered : isIntersecting,
  };
};

// 用于图片懒加载的专用 Hook
export const useImageLazyLoading = () => {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);
  const { elementRef, isVisible } = useLazyLoading({
    threshold: 0.1,
    rootMargin: "100px",
    triggerOnce: true,
  });

  const handleLoad = React.useCallback(() => {
    setIsLoaded(true);
    setHasError(false);
  }, []);

  const handleError = React.useCallback(() => {
    setIsLoaded(false);
    setHasError(true);
  }, []);

  return {
    elementRef,
    isVisible,
    isLoaded,
    hasError,
    handleLoad,
    handleError,
  };
};

// 用于无限滚动的 Hook
export const useInfiniteScroll = <T>(
  items: T[],
  loadMore: () => Promise<T[]>,
  options: {
    initialLoad?: number;
    loadIncrement?: number;
    hasMore?: boolean;
    loading?: boolean;
  } = {}
) => {
  const {
    initialLoad = 12,
    loadIncrement = 12,
    hasMore = true,
    loading = false,
  } = options;

  const [displayedItems, setDisplayedItems] = React.useState<T[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [hasMoreItems, setHasMoreItems] = React.useState(hasMore);

  // 初始化显示的项目
  React.useEffect(() => {
    setDisplayedItems(items.slice(0, initialLoad));
    setHasMoreItems(items.length > initialLoad);
  }, [items, initialLoad]);

  const { elementRef, isVisible } = useLazyLoading({
    threshold: 0.5,
    rootMargin: "200px",
    triggerOnce: false,
  });

  // 当触发元素可见时加载更多
  React.useEffect(() => {
    if (isVisible && hasMoreItems && !isLoading && !loading) {
      loadMoreItems();
    }
  }, [isVisible, hasMoreItems, isLoading, loading]);

  const loadMoreItems = React.useCallback(async () => {
    if (isLoading || !hasMoreItems) return;

    setIsLoading(true);
    try {
      const currentLength = displayedItems.length;
      const nextItems = items.slice(currentLength, currentLength + loadIncrement);
      
      if (nextItems.length > 0) {
        setDisplayedItems(prev => [...prev, ...nextItems]);
        setHasMoreItems(currentLength + nextItems.length < items.length);
      } else {
        // 如果本地没有更多项目，尝试从服务器加载
        const newItems = await loadMore();
        if (newItems.length > 0) {
          setDisplayedItems(prev => [...prev, ...newItems.slice(0, loadIncrement)]);
          setHasMoreItems(newItems.length >= loadIncrement);
        } else {
          setHasMoreItems(false);
        }
      }
    } catch (error) {
      console.error('Failed to load more items:', error);
    } finally {
      setIsLoading(false);
    }
  }, [displayedItems, items, loadIncrement, loadMore, isLoading, hasMoreItems]);

  return {
    displayedItems,
    isLoading,
    hasMoreItems,
    loadMoreItems,
    triggerRef: elementRef,
  };
};

// 用于虚拟滚动的 Hook（适用于大量数据）
export const useVirtualScroll = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) => {
  const [scrollTop, setScrollTop] = React.useState(0);
  const scrollElementRef = React.useRef<HTMLDivElement>(null);

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = React.useMemo(() => {
    return items.slice(startIndex, endIndex + 1).map((item, index) => ({
      item,
      index: startIndex + index,
    }));
  }, [items, startIndex, endIndex]);

  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;

  const handleScroll = React.useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  return {
    scrollElementRef,
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
  };
};
