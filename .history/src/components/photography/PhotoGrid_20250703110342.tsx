"use client";

import * as React from "react";
import { Photo } from "@/types";
import { cn } from "@/lib/utils";
import { Image } from "@/components/ui/Image";
import { Card } from "@/components/ui/Card";
import { formatPhotoDate } from "@/lib/photography";
import { useImageLazyLoading } from "@/hooks/useLazyLoading";

interface PhotoGridProps {
  photos: Photo[];
  onPhotoClick: (index: number) => void;
  columns?: 2 | 3 | 4 | 5;
  showInfo?: boolean;
  className?: string;
  loading?: boolean;
}

export const PhotoGrid: React.FC<PhotoGridProps> = ({
  photos,
  onPhotoClick,
  columns = 4,
  showInfo = false,
  className,
  loading = false,
}) => {
  const [loadedImages, setLoadedImages] = React.useState<Set<string>>(new Set());

  const handleImageLoad = (photoId: string) => {
    setLoadedImages(prev => new Set(prev).add(photoId));
  };

  const getGridColumns = () => {
    switch (columns) {
      case 2:
        return "grid-cols-1 sm:grid-cols-2";
      case 3:
        return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3";
      case 4:
        return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4";
      case 5:
        return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5";
      default:
        return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4";
    }
  };

  if (loading) {
    return (
      <div className={cn("grid gap-4", getGridColumns(), className)}>
        {Array.from({ length: 8 }).map((_, index) => (
          <Card key={index} className="overflow-hidden">
            <div className="aspect-square bg-muted animate-pulse" />
            {showInfo && (
              <div className="p-4 space-y-2">
                <div className="h-4 bg-muted animate-pulse rounded" />
                <div className="h-3 bg-muted animate-pulse rounded w-2/3" />
              </div>
            )}
          </Card>
        ))}
      </div>
    );
  }

  if (photos.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">📷</div>
        <h3 className="text-lg font-semibold text-foreground mb-2">暂无照片</h3>
        <p className="text-muted-foreground">这个分类下还没有照片</p>
      </div>
    );
  }

  return (
    <div className={cn("grid gap-4", getGridColumns(), className)}>
      {photos.map((photo, index) => (
        <PhotoGridItem
          key={photo.id}
          photo={photo}
          index={index}
          onClick={onPhotoClick}
          showInfo={showInfo}
          isLoaded={loadedImages.has(photo.id)}
          onLoad={() => handleImageLoad(photo.id)}
        />
      ))}
    </div>
  );
};

interface PhotoGridItemProps {
  photo: Photo;
  index: number;
  onClick: (index: number) => void;
  showInfo: boolean;
  isLoaded: boolean;
  onLoad: () => void;
}

const PhotoGridItem: React.FC<PhotoGridItemProps> = ({
  photo,
  index,
  onClick,
  showInfo,
  isLoaded,
  onLoad,
}) => {
  const { elementRef, isVisible, handleLoad, handleError } = useImageLazyLoading();

  const handleImageLoad = () => {
    handleLoad();
    onLoad();
  };

  return (
    <Card
      ref={elementRef}
      className="overflow-hidden cursor-pointer group hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
      onClick={() => onClick(index)}
    >
      <div className="aspect-square relative overflow-hidden bg-muted">
        {isVisible ? (
          <Image
            src={photo.src}
            alt={photo.alt}
            fill
            className={cn(
              "object-cover transition-all duration-300 group-hover:scale-105",
              isLoaded ? "opacity-100" : "opacity-0"
            )}
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, 25vw"
            onLoad={handleImageLoad}
            onError={handleError}
          />
        ) : (
          <div className="absolute inset-0 flex items-center justify-center bg-muted">
            <div className="text-4xl opacity-50">📷</div>
          </div>
        )}
        
        {/* 悬停遮罩 */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
        
        {/* 悬停信息 */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="text-white text-center">
            <div className="text-2xl mb-2">🔍</div>
            <div className="text-sm font-medium">{photo.title}</div>
          </div>
        </div>

        {/* 精选标识 */}
        {photo.featured && (
          <div className="absolute top-2 right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">
            ⭐ 精选
          </div>
        )}

        {/* 加载指示器 */}
        {!isLoaded && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-muted-foreground border-t-transparent rounded-full animate-spin" />
          </div>
        )}
      </div>

      {/* 照片信息 */}
      {showInfo && (
        <div className="p-4">
          <h3 className="font-semibold text-foreground mb-1 line-clamp-1">
            {photo.title}
          </h3>
          {photo.description && (
            <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
              {photo.description}
            </p>
          )}
          
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>{formatPhotoDate(photo.date)}</span>
            {photo.location && (
              <span className="flex items-center gap-1">
                📍 {photo.location}
              </span>
            )}
          </div>

          {/* 标签 */}
          {photo.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {photo.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag}
                  className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md"
                >
                  #{tag}
                </span>
              ))}
              {photo.tags.length > 3 && (
                <span className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md">
                  +{photo.tags.length - 3}
                </span>
              )}
            </div>
          )}

          {/* 拍摄信息 */}
          {photo.camera && (
            <div className="mt-2 text-xs text-muted-foreground">
              📷 {photo.camera}
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

// 瀑布流布局组件
export const PhotoMasonry: React.FC<PhotoGridProps> = ({
  photos,
  onPhotoClick,
  columns = 3,
  className,
  loading = false,
}) => {
  const [loadedImages, setLoadedImages] = React.useState<Set<string>>(new Set());

  const handleImageLoad = (photoId: string) => {
    setLoadedImages(prev => new Set(prev).add(photoId));
  };

  if (loading) {
    return (
      <div className={cn("columns-1 sm:columns-2 lg:columns-3 xl:columns-4 gap-4", className)}>
        {Array.from({ length: 12 }).map((_, index) => (
          <div key={index} className="break-inside-avoid mb-4">
            <Card className="overflow-hidden">
              <div 
                className="bg-muted animate-pulse"
                style={{ height: `${200 + Math.random() * 200}px` }}
              />
            </Card>
          </div>
        ))}
      </div>
    );
  }

  if (photos.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">📷</div>
        <h3 className="text-lg font-semibold text-foreground mb-2">暂无照片</h3>
        <p className="text-muted-foreground">这个分类下还没有照片</p>
      </div>
    );
  }

  const getColumnClass = () => {
    switch (columns) {
      case 2:
        return "columns-1 sm:columns-2";
      case 3:
        return "columns-1 sm:columns-2 lg:columns-3";
      case 4:
        return "columns-1 sm:columns-2 lg:columns-3 xl:columns-4";
      case 5:
        return "columns-1 sm:columns-2 lg:columns-3 xl:columns-4 2xl:columns-5";
      default:
        return "columns-1 sm:columns-2 lg:columns-3";
    }
  };

  return (
    <div className={cn(getColumnClass(), "gap-4", className)}>
      {photos.map((photo, index) => (
        <div key={photo.id} className="break-inside-avoid mb-4">
          <Card 
            className="overflow-hidden cursor-pointer group hover:shadow-lg transition-all duration-300"
            onClick={() => onPhotoClick(index)}
          >
            <div className="relative overflow-hidden bg-muted">
              <Image
                src={photo.src}
                alt={photo.alt}
                width={400}
                height={300}
                className={cn(
                  "w-full h-auto object-cover transition-all duration-300 group-hover:scale-105",
                  loadedImages.has(photo.id) ? "opacity-100" : "opacity-0"
                )}
                onLoad={() => handleImageLoad(photo.id)}
              />
              
              {/* 悬停遮罩 */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
              
              {/* 悬停信息 */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="text-white text-center">
                  <div className="text-2xl mb-2">🔍</div>
                  <div className="text-sm font-medium">{photo.title}</div>
                </div>
              </div>

              {/* 精选标识 */}
              {photo.featured && (
                <div className="absolute top-2 right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">
                  ⭐ 精选
                </div>
              )}

              {/* 加载指示器 */}
              {!loadedImages.has(photo.id) && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-8 h-8 border-2 border-muted-foreground border-t-transparent rounded-full animate-spin" />
                </div>
              )}
            </div>
          </Card>
        </div>
      ))}
    </div>
  );
};
