"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Button } from "./Button";
import { useTheme, getThemeIcon, getThemeLabel } from "@/components/providers/ThemeProvider";

interface ThemeToggleProps {
  className?: string;
  showLabel?: boolean;
  variant?: "icon" | "button" | "dropdown";
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className,
  showLabel = false,
  variant = "icon"
}) => {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  const toggleTheme = () => {
    const themes = ["light", "dark", "system"] as const;
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  // Don't render anything until mounted to avoid hydration mismatch
  if (!mounted) {
    return (
      <Button
        variant="ghost"
        size={variant === "icon" ? "icon" : "sm"}
        className={cn(variant === "icon" ? "w-9 h-9" : "", className)}
        disabled
      >
        <span className="text-lg">💻</span>
        {showLabel && <span className="ml-2">加载中...</span>}
      </Button>
    );
  }

  if (variant === "dropdown") {
    return (
      <div className={cn("relative", className)}>
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleTheme}
          className="w-full justify-start"
          title={getThemeLabel(theme)}
          aria-label={`切换主题，当前：${getThemeLabel(theme)}`}
        >
          <span className="text-lg mr-2">{getThemeIcon(theme)}</span>
          {getThemeLabel(theme)}
        </Button>
      </div>
    );
  }

  return (
    <Button
      variant="ghost"
      size={variant === "icon" ? "icon" : "sm"}
      onClick={toggleTheme}
      className={cn(
        variant === "icon" ? "w-9 h-9" : "",
        "transition-colors duration-200",
        className
      )}
      title={getThemeLabel(theme)}
      aria-label={`切换主题，当前：${getThemeLabel(theme)}`}
    >
      <span className="text-lg">{getThemeIcon(theme)}</span>
      {showLabel && (
        <span className="ml-2 text-sm font-medium">
          {getThemeLabel(theme)}
        </span>
      )}
    </Button>
  );
};

// Theme selector component with all options
export const ThemeSelector: React.FC<{ className?: string }> = ({ className }) => {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className={cn("flex gap-2", className)}>
        {["light", "dark", "system"].map((t) => (
          <Button key={t} variant="ghost" size="sm" disabled>
            <span className="text-lg mr-2">💻</span>
            加载中...
          </Button>
        ))}
      </div>
    );
  }

  const themes = [
    { value: "light" as const, label: "浅色", icon: "☀️" },
    { value: "dark" as const, label: "深色", icon: "🌙" },
    { value: "system" as const, label: "系统", icon: "💻" },
  ];

  return (
    <div className={cn("flex gap-1 p-1 bg-muted rounded-lg", className)}>
      {themes.map((themeOption) => (
        <Button
          key={themeOption.value}
          variant={theme === themeOption.value ? "default" : "ghost"}
          size="sm"
          onClick={() => setTheme(themeOption.value)}
          className={cn(
            "flex-1 transition-all duration-200",
            theme === themeOption.value && "shadow-sm"
          )}
          title={getThemeLabel(themeOption.value)}
        >
          <span className="text-sm mr-1">{themeOption.icon}</span>
          <span className="text-xs">{themeOption.label}</span>
        </Button>
      ))}
    </div>
  );
};

export { ThemeToggle };
