"use client";

import * as React from "react";
import NextImage, { ImageProps as NextImageProps } from "next/image";
import { cn } from "@/lib/utils";
import { PERFORMANCE_CONFIG } from "@/lib/constants";

export interface ImageProps extends Omit<NextImageProps, "onLoad" | "onError"> {
  fallback?: string;
  showLoading?: boolean;
  loadingClassName?: string;
  errorClassName?: string;
  onLoad?: () => void;
  onError?: () => void;
  // 性能优化选项
  eager?: boolean; // 是否立即加载（跳过懒加载）
  quality?: number; // 图片质量
  blur?: boolean; // 是否显示模糊占位符
  blurDataURL?: string; // 自定义模糊占位符
}

const Image = React.forwardRef<HTMLImageElement, ImageProps>(
  ({
    className,
    alt,
    src,
    fallback = "/images/placeholder.jpg",
    showLoading = true,
    loadingClassName,
    errorClassName,
    onLoad,
    onError,
    eager = false,
    quality = PERFORMANCE_CONFIG.imageOptimization.quality,
    blur = true,
    blurDataURL,
    sizes,
    priority,
    ...props
  }, ref) => {
    const [isLoading, setIsLoading] = React.useState(true);
    const [hasError, setHasError] = React.useState(false);
    const [imageSrc, setImageSrc] = React.useState(src);
    const [loadStartTime, setLoadStartTime] = React.useState<number>(0);

    const handleLoad = React.useCallback(() => {
      setIsLoading(false);
      setHasError(false);

      // 性能监控
      if (loadStartTime > 0) {
        const loadTime = performance.now() - loadStartTime;
        if (process.env.NODE_ENV === 'development') {
          console.log(`Image loaded in ${loadTime.toFixed(2)}ms:`, src);
        }
      }

      onLoad?.();
    }, [onLoad, loadStartTime, src]);

    const handleError = React.useCallback(() => {
      setIsLoading(false);
      setHasError(true);
      setImageSrc(fallback);

      if (process.env.NODE_ENV === 'development') {
        console.warn('Image failed to load:', src);
      }

      onError?.();
    }, [fallback, onError, src]);

    React.useEffect(() => {
      setImageSrc(src);
      setIsLoading(true);
      setHasError(false);
      setLoadStartTime(performance.now());
    }, [src]);

    // 生成响应式 sizes 属性
    const defaultSizes = React.useMemo(() => {
      return "(max-width: 640px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, 25vw";
    }, []);
    const responsiveSizes = sizes || defaultSizes;

    // 生成模糊占位符
    const placeholderProps = React.useMemo(() => {
      if (!blur) return {};

      if (blurDataURL) {
        return {
          placeholder: "blur" as const,
          blurDataURL,
        };
      }

      // 生成简单的模糊占位符 - 只在客户端执行
      if (typeof window !== 'undefined') {
        const canvas = document.createElement('canvas');
        canvas.width = 10;
        canvas.height = 10;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.fillStyle = '#f3f4f6';
          ctx.fillRect(0, 0, 10, 10);
          return {
            placeholder: "blur" as const,
            blurDataURL: canvas.toDataURL(),
          };
        }
      }

      return {};
    }, [blur, blurDataURL]);

    return (
      <div className={cn("relative overflow-hidden", className)}>
        {isLoading && showLoading && (
          <div
            className={cn(
              "absolute inset-0 flex items-center justify-center bg-muted animate-pulse",
              loadingClassName
            )}
          >
            <div className="w-8 h-8 border-2 border-muted-foreground border-t-transparent rounded-full animate-spin" />
          </div>
        )}
        
        <NextImage
          ref={ref}
          src={imageSrc}
          alt={alt}
          className={cn(
            "transition-opacity duration-300",
            isLoading ? "opacity-0" : "opacity-100",
            hasError && errorClassName
          )}
          onLoad={handleLoad}
          onError={handleError}
          quality={quality}
          sizes={responsiveSizes}
          priority={priority || eager}
          loading={eager ? "eager" : "lazy"}
          {...placeholderProps}
          {...props}
        />
        
        {hasError && (
          <div
            className={cn(
              "absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground",
              errorClassName
            )}
          >
            <div className="text-center">
              <div className="text-2xl mb-2">📷</div>
              <div className="text-sm">图片加载失败</div>
            </div>
          </div>
        )}
      </div>
    );
  }
);

Image.displayName = "Image";

export { Image };
