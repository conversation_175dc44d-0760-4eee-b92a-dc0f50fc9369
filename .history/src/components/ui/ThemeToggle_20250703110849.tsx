"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Button } from "./Button";
import { useTheme, getThemeIcon, getThemeLabel } from "@/components/providers/ThemeProvider";

interface ThemeToggleProps {
  className?: string;
  showLabel?: boolean;
  variant?: "icon" | "button" | "dropdown";
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className,
  showLabel = false,
  variant = "icon"
}) => {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  const toggleTheme = () => {
    const themes = ["light", "dark", "system"] as const;
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  // Don't render anything until mounted to avoid hydration mismatch
  if (!mounted) {
    return (
      <Button
        variant="ghost"
        size={variant === "icon" ? "icon" : "sm"}
        className={cn(variant === "icon" ? "w-9 h-9" : "", className)}
        disabled
      >
        <span className="text-lg">💻</span>
        {showLabel && <span className="ml-2">加载中...</span>}
      </Button>
    );
  }

  if (variant === "dropdown") {
    return (
      <div className={cn("relative", className)}>
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleTheme}
          className="w-full justify-start"
          title={getThemeLabel(theme)}
          aria-label={`切换主题，当前：${getThemeLabel(theme)}`}
        >
          <span className="text-lg mr-2">{getThemeIcon(theme)}</span>
          {getThemeLabel(theme)}
        </Button>
      </div>
    );
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className={cn("w-9 h-9", className)}
      title={getThemeLabel()}
      aria-label={`切换主题，当前：${getThemeLabel()}`}
    >
      <span className="text-lg">{getThemeIcon()}</span>
    </Button>
  );
};

export { ThemeToggle };
