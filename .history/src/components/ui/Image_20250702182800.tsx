"use client";

import * as React from "react";
import NextImage, { ImageProps as NextImageProps } from "next/image";
import { cn } from "@/lib/utils";

export interface ImageProps extends Omit<NextImageProps, "onLoad" | "onError"> {
  fallback?: string;
  showLoading?: boolean;
  loadingClassName?: string;
  errorClassName?: string;
  onLoad?: () => void;
  onError?: () => void;
}

const Image = React.forwardRef<HTMLImageElement, ImageProps>(
  ({
    className,
    alt,
    src,
    fallback = "/images/placeholder.jpg",
    showLoading = true,
    loadingClassName,
    errorClassName,
    onLoad,
    onError,
    ...props
  }, ref) => {
    const [isLoading, setIsLoading] = React.useState(true);
    const [hasError, setHasError] = React.useState(false);
    const [imageSrc, setImageSrc] = React.useState(src);

    const handleLoad = React.useCallback(() => {
      setIsLoading(false);
      setHasError(false);
      onLoad?.();
    }, [onLoad]);

    const handleError = React.useCallback(() => {
      setIsLoading(false);
      setHasError(true);
      setImageSrc(fallback);
      onError?.();
    }, [fallback, onError]);

    React.useEffect(() => {
      setImageSrc(src);
      setIsLoading(true);
      setHasError(false);
    }, [src]);

    return (
      <div className={cn("relative overflow-hidden", className)}>
        {isLoading && showLoading && (
          <div
            className={cn(
              "absolute inset-0 flex items-center justify-center bg-muted animate-pulse",
              loadingClassName
            )}
          >
            <div className="w-8 h-8 border-2 border-muted-foreground border-t-transparent rounded-full animate-spin" />
          </div>
        )}
        
        <NextImage
          ref={ref}
          src={imageSrc}
          alt={alt}
          className={cn(
            "transition-opacity duration-300",
            isLoading ? "opacity-0" : "opacity-100",
            hasError && errorClassName
          )}
          onLoad={handleLoad}
          onError={handleError}
          {...props}
        />
        
        {hasError && (
          <div
            className={cn(
              "absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground",
              errorClassName
            )}
          >
            <div className="text-center">
              <div className="text-2xl mb-2">📷</div>
              <div className="text-sm">图片加载失败</div>
            </div>
          </div>
        )}
      </div>
    );
  }
);

Image.displayName = "Image";

export { Image };
