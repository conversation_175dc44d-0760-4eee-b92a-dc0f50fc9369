"use client";

import * as React from "react";
import { DEV_CONFIG } from "@/lib/constants";

interface PerformanceMetrics {
  // Core Web Vitals
  LCP?: number; // Largest Contentful Paint
  FID?: number; // First Input Delay
  CLS?: number; // Cumulative Layout Shift
  FCP?: number; // First Contentful Paint
  TTFB?: number; // Time to First Byte
  
  // Navigation Timing
  domContentLoaded?: number;
  loadComplete?: number;
  
  // Memory (if available)
  usedJSHeapSize?: number;
  totalJSHeapSize?: number;
  jsHeapSizeLimit?: number;
}

interface PerformanceEntry {
  name: string;
  entryType: string;
  startTime: number;
  duration: number;
  value?: number;
}

export const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = React.useState<PerformanceMetrics>({});
  const [isVisible, setIsVisible] = React.useState(false);

  React.useEffect(() => {
    if (!DEV_CONFIG.showDebugInfo) return;

    // 监听性能指标
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        const perfEntry = entry as PerformanceEntry;
        
        switch (perfEntry.entryType) {
          case 'largest-contentful-paint':
            setMetrics(prev => ({ ...prev, LCP: perfEntry.startTime }));
            break;
          case 'first-input':
            setMetrics(prev => ({ ...prev, FID: perfEntry.duration }));
            break;
          case 'layout-shift':
            if (!(entry as any).hadRecentInput) {
              setMetrics(prev => ({ 
                ...prev, 
                CLS: (prev.CLS || 0) + (perfEntry.value || 0)
              }));
            }
            break;
          case 'paint':
            if (perfEntry.name === 'first-contentful-paint') {
              setMetrics(prev => ({ ...prev, FCP: perfEntry.startTime }));
            }
            break;
          case 'navigation':
            const navEntry = entry as PerformanceNavigationTiming;
            setMetrics(prev => ({
              ...prev,
              TTFB: navEntry.responseStart - navEntry.requestStart,
              domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.fetchStart,
              loadComplete: navEntry.loadEventEnd - navEntry.fetchStart,
            }));
            break;
        }
      });
    });

    // 观察不同类型的性能指标
    try {
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
      observer.observe({ entryTypes: ['first-input'] });
      observer.observe({ entryTypes: ['layout-shift'] });
      observer.observe({ entryTypes: ['paint'] });
      observer.observe({ entryTypes: ['navigation'] });
    } catch (error) {
      console.warn('Performance Observer not supported:', error);
    }

    // 获取内存信息（如果可用）
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMetrics(prev => ({
          ...prev,
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
        }));
      }
    };

    updateMemoryInfo();
    const memoryInterval = setInterval(updateMemoryInfo, 5000);

    // 键盘快捷键切换显示
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'P') {
        setIsVisible(prev => !prev);
      }
    };

    document.addEventListener('keydown', handleKeyPress);

    return () => {
      observer.disconnect();
      clearInterval(memoryInterval);
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, []);

  if (!DEV_CONFIG.showDebugInfo || !isVisible) {
    return null;
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (time: number) => {
    return `${time.toFixed(2)}ms`;
  };

  const getScoreColor = (metric: string, value: number) => {
    switch (metric) {
      case 'LCP':
        return value <= 2500 ? 'text-green-500' : value <= 4000 ? 'text-yellow-500' : 'text-red-500';
      case 'FID':
        return value <= 100 ? 'text-green-500' : value <= 300 ? 'text-yellow-500' : 'text-red-500';
      case 'CLS':
        return value <= 0.1 ? 'text-green-500' : value <= 0.25 ? 'text-yellow-500' : 'text-red-500';
      case 'FCP':
        return value <= 1800 ? 'text-green-500' : value <= 3000 ? 'text-yellow-500' : 'text-red-500';
      case 'TTFB':
        return value <= 800 ? 'text-green-500' : value <= 1800 ? 'text-yellow-500' : 'text-red-500';
      default:
        return 'text-foreground';
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-background/95 backdrop-blur border rounded-lg p-4 shadow-lg max-w-sm">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-foreground">性能监控</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-muted-foreground hover:text-foreground text-xs"
        >
          ✕
        </button>
      </div>

      <div className="space-y-2 text-xs">
        {/* Core Web Vitals */}
        <div>
          <h4 className="font-medium text-foreground mb-1">Core Web Vitals</h4>
          <div className="grid grid-cols-2 gap-2">
            {metrics.LCP && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">LCP:</span>
                <span className={getScoreColor('LCP', metrics.LCP)}>
                  {formatTime(metrics.LCP)}
                </span>
              </div>
            )}
            {metrics.FID && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">FID:</span>
                <span className={getScoreColor('FID', metrics.FID)}>
                  {formatTime(metrics.FID)}
                </span>
              </div>
            )}
            {metrics.CLS !== undefined && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">CLS:</span>
                <span className={getScoreColor('CLS', metrics.CLS)}>
                  {metrics.CLS.toFixed(3)}
                </span>
              </div>
            )}
            {metrics.FCP && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">FCP:</span>
                <span className={getScoreColor('FCP', metrics.FCP)}>
                  {formatTime(metrics.FCP)}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Navigation Timing */}
        <div>
          <h4 className="font-medium text-foreground mb-1">加载时间</h4>
          <div className="space-y-1">
            {metrics.TTFB && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">TTFB:</span>
                <span className={getScoreColor('TTFB', metrics.TTFB)}>
                  {formatTime(metrics.TTFB)}
                </span>
              </div>
            )}
            {metrics.domContentLoaded && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">DOM Ready:</span>
                <span className="text-foreground">
                  {formatTime(metrics.domContentLoaded)}
                </span>
              </div>
            )}
            {metrics.loadComplete && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Load Complete:</span>
                <span className="text-foreground">
                  {formatTime(metrics.loadComplete)}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Memory Usage */}
        {metrics.usedJSHeapSize && (
          <div>
            <h4 className="font-medium text-foreground mb-1">内存使用</h4>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span className="text-muted-foreground">已使用:</span>
                <span className="text-foreground">
                  {formatBytes(metrics.usedJSHeapSize)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">总计:</span>
                <span className="text-foreground">
                  {formatBytes(metrics.totalJSHeapSize || 0)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">限制:</span>
                <span className="text-foreground">
                  {formatBytes(metrics.jsHeapSizeLimit || 0)}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="mt-3 pt-2 border-t text-xs text-muted-foreground">
        按 Ctrl+Shift+P 切换显示
      </div>
    </div>
  );
};

// 性能监控钩子
export const usePerformanceMonitoring = () => {
  const [metrics, setMetrics] = React.useState<PerformanceMetrics>({});

  React.useEffect(() => {
    // 获取当前页面的性能指标
    const getPageMetrics = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const paint = performance.getEntriesByType('paint');
      
      const fcp = paint.find(entry => entry.name === 'first-contentful-paint');
      
      return {
        TTFB: navigation.responseStart - navigation.requestStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
        loadComplete: navigation.loadEventEnd - navigation.fetchStart,
        FCP: fcp?.startTime,
      };
    };

    // 页面加载完成后获取指标
    if (document.readyState === 'complete') {
      setMetrics(getPageMetrics());
    } else {
      window.addEventListener('load', () => {
        setTimeout(() => {
          setMetrics(getPageMetrics());
        }, 0);
      });
    }
  }, []);

  return metrics;
};
