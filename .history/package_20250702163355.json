{"name": "zer-blog-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"gray-matter": "^4.0.3", "gsap": "^3.13.0", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "remark": "^15.0.1", "remark-html": "^16.0.1", "sharp": "^0.34.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.0", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}